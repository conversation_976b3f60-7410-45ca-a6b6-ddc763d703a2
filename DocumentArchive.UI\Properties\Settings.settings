<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="DocumentArchive.UI.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="WindowState" Type="System.Windows.Forms.FormWindowState" Scope="User">
      <Value Profile="(Default)">Normal</Value>
    </Setting>
    <Setting Name="WindowSize" Type="System.Drawing.Size" Scope="User">
      <Value Profile="(Default)">1200, 800</Value>
    </Setting>
    <Setting Name="WindowLocation" Type="System.Drawing.Point" Scope="User">
      <Value Profile="(Default)">-1, -1</Value>
    </Setting>
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">ar-SA</Value>
    </Setting>
    <Setting Name="PageSize" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">50</Value>
    </Setting>
    <Setting Name="AutoRefresh" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowPreview" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="DefaultDocumentType" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
  </Settings>
</SettingsFile>
