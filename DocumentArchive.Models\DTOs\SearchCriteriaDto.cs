using System;
using System.Collections.Generic;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for search criteria
    /// </summary>
    public class SearchCriteriaDto
    {
        public string SearchText { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public DocumentType? DocumentType { get; set; }
        public List<int> CategoryIds { get; set; } = new List<int>();
        public DocumentStatus? Status { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SortColumn { get; set; } = "Date";
        public bool SortDescending { get; set; } = true;
    }

    /// <summary>
    /// Paged result for search operations
    /// </summary>
    /// <typeparam name="T">Type of items in the result</typeparam>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
