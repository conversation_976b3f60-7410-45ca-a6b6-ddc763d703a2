using System.Collections.Generic;
using System.Threading.Tasks;

namespace DocumentArchive.Business.Interfaces
{
    /// <summary>
    /// Service interface for File operations
    /// </summary>
    public interface IFileService
    {
        /// <summary>
        /// Save uploaded file to storage
        /// </summary>
        Task<string> SaveFileAsync(string sourceFilePath, string originalFileName, int documentId, int year, string documentType);

        /// <summary>
        /// Delete file from storage
        /// </summary>
        Task<bool> DeleteFileAsync(string filePath);

        /// <summary>
        /// Copy file to new location
        /// </summary>
        Task<string> CopyFileAsync(string sourceFilePath, string destinationPath);

        /// <summary>
        /// Check if file exists
        /// </summary>
        bool FileExists(string filePath);

        /// <summary>
        /// Get file size
        /// </summary>
        long GetFileSize(string filePath);

        /// <summary>
        /// Get file MIME type
        /// </summary>
        string GetMimeType(string fileName);

        /// <summary>
        /// Validate file type
        /// </summary>
        bool IsValidFileType(string fileName);

        /// <summary>
        /// Get supported file extensions
        /// </summary>
        List<string> GetSupportedExtensions();

        /// <summary>
        /// Format file size for display
        /// </summary>
        string FormatFileSize(long bytes);

        /// <summary>
        /// Generate thumbnail for image files
        /// </summary>
        Task<string> GenerateThumbnailAsync(string filePath);

        /// <summary>
        /// Validate file integrity
        /// </summary>
        Task<bool> ValidateFileIntegrityAsync(string filePath);

        /// <summary>
        /// Get storage directory path
        /// </summary>
        string GetStorageDirectory(int year, string documentType);

        /// <summary>
        /// Clean up orphaned files
        /// </summary>
        Task<int> CleanupOrphanedFilesAsync();
    }
}
