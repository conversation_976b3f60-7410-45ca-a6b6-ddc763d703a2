using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using DocumentArchive.Data.Context;
using DocumentArchive.Data.Interfaces;
using DocumentArchive.Models.Entities;

namespace DocumentArchive.Data.Repositories
{
    /// <summary>
    /// Repository implementation for Category operations
    /// </summary>
    public class CategoryRepository : ICategoryRepository
    {
        private readonly DocumentArchiveContext _context;

        public CategoryRepository(DocumentArchiveContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Category> GetByIdAsync(int id)
        {
            return await _context.Categories
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<List<Category>> GetAllAsync()
        {
            return await _context.Categories
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<List<Category>> GetActiveAsync()
        {
            return await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Category> AddAsync(Category category)
        {
            if (category == null) throw new ArgumentNullException(nameof(category));

            category.CreatedDate = DateTime.Now;
            category.ModifiedDate = DateTime.Now;

            _context.Categories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<Category> UpdateAsync(Category category)
        {
            if (category == null) throw new ArgumentNullException(nameof(category));

            var existingCategory = await _context.Categories.FindAsync(category.Id);
            if (existingCategory == null) return null;

            existingCategory.Name = category.Name;
            existingCategory.Description = category.Description;
            existingCategory.IsActive = category.IsActive;
            existingCategory.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();
            return existingCategory;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var category = await _context.Categories.FindAsync(id);
            if (category == null) return false;

            // Check if category has documents
            var hasDocuments = await _context.Documents
                .AnyAsync(d => d.CategoryId == id);

            if (hasDocuments)
            {
                // Soft delete - set IsActive to false
                category.IsActive = false;
                category.ModifiedDate = DateTime.Now;
            }
            else
            {
                // Hard delete if no documents
                _context.Categories.Remove(category);
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> NameExistsAsync(string name, int? excludeId = null)
        {
            var query = _context.Categories.Where(c => c.Name.ToLower() == name.ToLower());
            
            if (excludeId.HasValue)
            {
                query = query.Where(c => c.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        public async Task<Category> GetWithDocumentCountAsync(int id)
        {
            return await _context.Categories
                .Where(c => c.Id == id)
                .Select(c => new Category
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    IsActive = c.IsActive,
                    CreatedDate = c.CreatedDate,
                    ModifiedDate = c.ModifiedDate,
                    Documents = c.Documents.Where(d => d.Status != Models.Enums.DocumentStatus.Deleted).ToList()
                })
                .FirstOrDefaultAsync();
        }

        public async Task<List<Category>> SearchAsync(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) return await GetActiveAsync();

            var searchLower = searchText.ToLower();
            return await _context.Categories
                .Where(c => c.IsActive && 
                           (c.Name.ToLower().Contains(searchLower) ||
                            c.Description.ToLower().Contains(searchLower)))
                .OrderBy(c => c.Name)
                .ToListAsync();
        }
    }
}
