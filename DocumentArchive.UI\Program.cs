using System;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Threading;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Business.Services;
using DocumentArchive.Data.Context;
using DocumentArchive.Data.Interfaces;
using DocumentArchive.Data.Repositories;
using DocumentArchive.UI.Forms;
using Microsoft.Extensions.DependencyInjection;
using NLog;

namespace DocumentArchive.UI
{
    internal static class Program
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private static ServiceProvider _serviceProvider;

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // Configure application
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // Set up Arabic culture
                SetupCulture();
                
                // Set up data directory
                SetupDataDirectory();
                
                // Configure dependency injection
                ConfigureServices();
                
                // Initialize database
                InitializeDatabase();
                
                // Set up global exception handling
                SetupExceptionHandling();
                
                Logger.Info("Application starting...");
                
                // Start the main form
                var mainForm = _serviceProvider.GetRequiredService<MainForm>();
                Application.Run(mainForm);
                
                Logger.Info("Application ended normally");
            }
            catch (Exception ex)
            {
                Logger.Fatal(ex, "Fatal error during application startup");
                MessageBox.Show($"خطأ فادح في بدء التطبيق:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _serviceProvider?.Dispose();
                LogManager.Shutdown();
            }
        }

        private static void SetupCulture()
        {
            // Set Arabic culture for RTL support
            var culture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            
            // Set RTL for the application
            Application.CurrentCulture = culture;
        }

        private static void SetupDataDirectory()
        {
            // Set up the data directory for SQLite database
            var dataDirectory = Path.Combine(Application.StartupPath, "Data");
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
            }
            AppDomain.CurrentDomain.SetData("DataDirectory", dataDirectory);
        }

        private static void ConfigureServices()
        {
            var services = new ServiceCollection();
            
            // Register DbContext
            services.AddTransient<DocumentArchiveContext>();
            
            // Register repositories
            services.AddTransient<IDocumentRepository, DocumentRepository>();
            services.AddTransient<ICategoryRepository, CategoryRepository>();
            services.AddTransient<IUnitOfWork, UnitOfWork>();
            
            // Register services
            services.AddTransient<IDocumentService, DocumentService>();
            services.AddTransient<ICategoryService, CategoryService>();
            services.AddTransient<IFileService, FileService>();
            services.AddTransient<IExportService, ExportService>();

            // Register forms
            services.AddTransient<MainForm>();
            services.AddTransient<DocumentForm>();
            services.AddTransient<CategoryForm>();
            services.AddTransient<DocumentViewerForm>();
            services.AddTransient<ReportsForm>();
            services.AddTransient<AdvancedSearchForm>();
            
            _serviceProvider = services.BuildServiceProvider();
        }

        private static void InitializeDatabase()
        {
            try
            {
                using (var context = _serviceProvider.GetRequiredService<DocumentArchiveContext>())
                {
                    // Enable automatic migrations
                    Database.SetInitializer(new MigrateDatabaseToLatestVersion<DocumentArchiveContext, 
                        DocumentArchive.Data.Migrations.Configuration>());
                    
                    // Force database creation/migration
                    context.Database.Initialize(force: false);
                    
                    Logger.Info("Database initialized successfully");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error initializing database");
                throw;
            }
        }

        private static void SetupExceptionHandling()
        {
            // Handle unhandled exceptions in UI thread
            Application.ThreadException += (sender, e) =>
            {
                Logger.Error(e.Exception, "Unhandled UI thread exception");
                ShowErrorMessage("حدث خطأ غير متوقع في واجهة المستخدم", e.Exception);
            };

            // Handle unhandled exceptions in non-UI threads
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                var exception = e.ExceptionObject as Exception;
                Logger.Fatal(exception, "Unhandled domain exception");
                
                if (e.IsTerminating)
                {
                    ShowErrorMessage("حدث خطأ فادح وسيتم إغلاق التطبيق", exception);
                }
            };

            // Set unhandled exception mode
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
        }

        private static void ShowErrorMessage(string message, Exception exception)
        {
            var fullMessage = $"{message}\n\nتفاصيل الخطأ:\n{exception?.Message}";
            
            if (exception != null)
            {
                fullMessage += $"\n\nمعلومات إضافية:\n{exception.GetType().Name}";
                
                if (exception.InnerException != null)
                {
                    fullMessage += $"\n\nالخطأ الداخلي:\n{exception.InnerException.Message}";
                }
            }

            MessageBox.Show(fullMessage, "خطأ في التطبيق", 
                MessageBoxButtons.OK, MessageBoxIcon.Error, 
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        public static T GetService<T>()
        {
            return _serviceProvider.GetRequiredService<T>();
        }

        public static object GetService(Type serviceType)
        {
            return _serviceProvider.GetRequiredService(serviceType);
        }
    }
}
