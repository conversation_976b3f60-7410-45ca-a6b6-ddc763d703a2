using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.UI.Extensions;
using DocumentArchive.UI.Helpers;

namespace DocumentArchive.UI.Forms
{
    public partial class CategoryForm : Form
    {
        private readonly ICategoryService _categoryService;
        private CategoryDto _currentCategory;
        private bool _isEditMode;
        private bool _hasUnsavedChanges;

        public event EventHandler CategoriesChanged;

        public CategoryForm(ICategoryService categoryService)
        {
            _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
            
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Configure form for Arabic
            UIHelper.ConfigureFormForArabic(this);
            
            // Style controls
            UIHelper.StyleDataGridView(categoriesDataGridView);
            UIHelper.StylePrimaryButton(saveButton);
            UIHelper.StyleSecondaryButton(cancelButton);
            UIHelper.StyleSecondaryButton(addButton);
            UIHelper.StyleWarningButton(editButton);
            UIHelper.StyleErrorButton(deleteButton);
            
            UIHelper.StyleTextBox(nameTextBox);
            UIHelper.StyleTextBox(descriptionTextBox);
            
            // Set placeholders
            nameTextBox.SetPlaceholder("أدخل اسم الفئة...");
            descriptionTextBox.SetPlaceholder("أدخل وصف الفئة (اختياري)...");
            
            // Wire up events
            nameTextBox.TextChanged += OnFormDataChanged;
            descriptionTextBox.TextChanged += OnFormDataChanged;
            isActiveCheckBox.CheckedChanged += OnFormDataChanged;
            
            // Initialize buttons state
            UpdateButtonsState();
        }

        private async void CategoryForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadCategoriesAsync();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الفئات");
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                var loadingForm = MessageHelper.ShowLoadingMessage("جاري تحميل الفئات...");
                
                try
                {
                    var categories = await _categoryService.GetAllAsync();
                    categoriesDataGridView.DataSource = categories;
                    ConfigureDataGridViewColumns();
                    UpdateStatusLabel();
                }
                finally
                {
                    MessageHelper.CloseLoadingMessage(loadingForm);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الفئات");
            }
        }

        private void ConfigureDataGridViewColumns()
        {
            if (categoriesDataGridView.Columns.Count == 0) return;

            // Hide unnecessary columns
            if (categoriesDataGridView.Columns["Id"] != null)
                categoriesDataGridView.Columns["Id"].Visible = false;

            // Configure visible columns
            var columnConfig = new System.Collections.Generic.Dictionary<string, (string Header, int Width)>
            {
                { "Name", ("اسم الفئة", 200) },
                { "Description", ("الوصف", 300) },
                { "IsActive", ("نشطة", 80) },
                { "DocumentCount", ("عدد الوثائق", 100) },
                { "CreatedDate", ("تاريخ الإنشاء", 120) },
                { "ModifiedDate", ("تاريخ التعديل", 120) }
            };

            foreach (var config in columnConfig)
            {
                if (categoriesDataGridView.Columns[config.Key] != null)
                {
                    var column = categoriesDataGridView.Columns[config.Key];
                    column.HeaderText = config.Value.Header;
                    column.Width = config.Value.Width;
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                }
            }

            // Format date columns
            if (categoriesDataGridView.Columns["CreatedDate"] != null)
            {
                categoriesDataGridView.Columns["CreatedDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
            
            if (categoriesDataGridView.Columns["ModifiedDate"] != null)
            {
                categoriesDataGridView.Columns["ModifiedDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }

            // Format boolean column
            if (categoriesDataGridView.Columns["IsActive"] != null)
            {
                categoriesDataGridView.Columns["IsActive"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }
        }

        private void UpdateStatusLabel()
        {
            var totalCount = categoriesDataGridView.Rows.Count;
            var activeCount = 0;
            
            foreach (DataGridViewRow row in categoriesDataGridView.Rows)
            {
                if (row.DataBoundItem is CategoryDto category && category.IsActive)
                {
                    activeCount++;
                }
            }
            
            statusLabel.Text = $"إجمالي الفئات: {totalCount} | النشطة: {activeCount}";
        }

        private void UpdateButtonsState()
        {
            var hasSelection = categoriesDataGridView.SelectedRows.Count > 0;
            editButton.Enabled = categoriesDataGridView.SelectedRows.Count == 1;
            deleteButton.Enabled = hasSelection;
            
            saveButton.Enabled = !string.IsNullOrWhiteSpace(nameTextBox.Text);
        }

        private void OnFormDataChanged(object sender, EventArgs e)
        {
            _hasUnsavedChanges = true;
            UpdateButtonsState();
        }

        private void categoriesDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonsState();
        }

        private void addButton_Click(object sender, EventArgs e)
        {
            StartNewCategory();
        }

        private void editButton_Click(object sender, EventArgs e)
        {
            if (categoriesDataGridView.SelectedRows.Count == 1)
            {
                var selectedCategory = (CategoryDto)categoriesDataGridView.SelectedRows[0].DataBoundItem;
                StartEditCategory(selectedCategory);
            }
        }

        private async void deleteButton_Click(object sender, EventArgs e)
        {
            if (categoriesDataGridView.SelectedRows.Count == 0) return;

            var selectedCategories = categoriesDataGridView.SelectedRows
                .Cast<DataGridViewRow>()
                .Select(row => (CategoryDto)row.DataBoundItem)
                .ToList();

            var message = selectedCategories.Count == 1
                ? $"هل أنت متأكد من رغبتك في حذف الفئة '{selectedCategories[0].Name}'؟"
                : $"هل أنت متأكد من رغبتك في حذف {selectedCategories.Count} فئة؟";

            if (selectedCategories.Any(c => c.DocumentCount > 0))
            {
                message += "\n\nتحتوي بعض الفئات على وثائق. سيتم إلغاء تفعيلها بدلاً من حذفها.";
            }

            if (MessageHelper.ShowConfirmation(message))
            {
                await DeleteCategoriesAsync(selectedCategories);
            }
        }

        private async Task DeleteCategoriesAsync(System.Collections.Generic.List<CategoryDto> categories)
        {
            var loadingForm = MessageHelper.ShowLoadingMessage("جاري حذف الفئات...");
            
            try
            {
                var successCount = 0;
                var errorCount = 0;

                foreach (var category in categories)
                {
                    try
                    {
                        var success = await _categoryService.DeleteAsync(category.Id);
                        if (success)
                            successCount++;
                        else
                            errorCount++;
                    }
                    catch
                    {
                        errorCount++;
                    }
                }

                if (successCount > 0)
                {
                    await LoadCategoriesAsync();
                    ClearForm();
                    CategoriesChanged?.Invoke(this, EventArgs.Empty);
                    MessageHelper.ShowSuccess($"تم حذف/إلغاء تفعيل {successCount} فئة بنجاح");
                }

                if (errorCount > 0)
                {
                    MessageHelper.ShowWarning($"فشل في حذف {errorCount} فئة");
                }
            }
            finally
            {
                MessageHelper.CloseLoadingMessage(loadingForm);
            }
        }

        private void StartNewCategory()
        {
            _currentCategory = null;
            _isEditMode = false;
            ClearForm();
            nameTextBox.Focus();
        }

        private void StartEditCategory(CategoryDto category)
        {
            _currentCategory = category;
            _isEditMode = true;
            LoadCategoryData();
            nameTextBox.Focus();
        }

        private void LoadCategoryData()
        {
            if (_currentCategory == null) return;

            // Temporarily disable change events
            nameTextBox.TextChanged -= OnFormDataChanged;
            descriptionTextBox.TextChanged -= OnFormDataChanged;
            isActiveCheckBox.CheckedChanged -= OnFormDataChanged;

            try
            {
                nameTextBox.Text = _currentCategory.Name;
                descriptionTextBox.Text = _currentCategory.Description ?? string.Empty;
                isActiveCheckBox.Checked = _currentCategory.IsActive;
                
                _hasUnsavedChanges = false;
            }
            finally
            {
                // Re-enable change events
                nameTextBox.TextChanged += OnFormDataChanged;
                descriptionTextBox.TextChanged += OnFormDataChanged;
                isActiveCheckBox.CheckedChanged += OnFormDataChanged;
            }
        }

        private void ClearForm()
        {
            // Temporarily disable change events
            nameTextBox.TextChanged -= OnFormDataChanged;
            descriptionTextBox.TextChanged -= OnFormDataChanged;
            isActiveCheckBox.CheckedChanged -= OnFormDataChanged;

            try
            {
                nameTextBox.Clear();
                descriptionTextBox.Clear();
                isActiveCheckBox.Checked = true;
                
                _hasUnsavedChanges = false;
            }
            finally
            {
                // Re-enable change events
                nameTextBox.TextChanged += OnFormDataChanged;
                descriptionTextBox.TextChanged += OnFormDataChanged;
                isActiveCheckBox.CheckedChanged += OnFormDataChanged;
            }
        }

        private async void saveButton_Click(object sender, EventArgs e)
        {
            await SaveCategoryAsync();
        }

        private async Task SaveCategoryAsync()
        {
            try
            {
                // Validate form
                var validationErrors = ValidateForm();
                if (validationErrors.Any())
                {
                    MessageHelper.ShowValidationErrors(validationErrors);
                    return;
                }

                var loadingForm = MessageHelper.ShowLoadingMessage("جاري حفظ الفئة...");
                
                try
                {
                    var categoryDto = CreateCategoryDto();
                    
                    CategoryDto savedCategory;
                    if (_isEditMode)
                    {
                        savedCategory = await _categoryService.UpdateAsync(categoryDto);
                    }
                    else
                    {
                        savedCategory = await _categoryService.CreateAsync(categoryDto);
                    }

                    if (savedCategory != null)
                    {
                        await LoadCategoriesAsync();
                        ClearForm();
                        CategoriesChanged?.Invoke(this, EventArgs.Empty);
                        
                        MessageHelper.ShowSuccess(_isEditMode ? "تم تحديث الفئة بنجاح" : "تم إنشاء الفئة بنجاح");
                    }
                }
                finally
                {
                    MessageHelper.CloseLoadingMessage(loadingForm);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في حفظ الفئة");
            }
        }

        private System.Collections.Generic.List<string> ValidateForm()
        {
            var errors = new System.Collections.Generic.List<string>();

            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
                errors.Add("اسم الفئة مطلوب");

            return errors;
        }

        private CategoryDto CreateCategoryDto()
        {
            return new CategoryDto
            {
                Id = _currentCategory?.Id ?? 0,
                Name = nameTextBox.Text.Trim(),
                Description = descriptionTextBox.Text.Trim(),
                IsActive = isActiveCheckBox.Checked
            };
        }

        private void cancelButton_Click(object sender, EventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageHelper.ShowUnsavedChangesConfirmation();
                if (result == DialogResult.Yes)
                {
                    saveButton_Click(sender, e);
                    return;
                }
                else if (result == DialogResult.Cancel)
                {
                    return;
                }
            }
            
            ClearForm();
        }

        private void categoriesDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                editButton_Click(sender, e);
            }
        }

        private void CategoryForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageHelper.ShowUnsavedChangesConfirmation();
                if (result == DialogResult.Yes)
                {
                    e.Cancel = true;
                    saveButton_Click(sender, e);
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
        }
    }
}
