using System;
using System.Data.Entity;
using System.Threading.Tasks;
using DocumentArchive.Data.Context;
using DocumentArchive.Data.Interfaces;

namespace DocumentArchive.Data.Repositories
{
    /// <summary>
    /// Unit of Work implementation
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly DocumentArchiveContext _context;
        private DbContextTransaction _transaction;
        private bool _disposed = false;

        private IDocumentRepository _documents;
        private ICategoryRepository _categories;

        public UnitOfWork(DocumentArchiveContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public IDocumentRepository Documents
        {
            get
            {
                if (_documents == null)
                {
                    _documents = new DocumentRepository(_context);
                }
                return _documents;
            }
        }

        public ICategoryRepository Categories
        {
            get
            {
                if (_categories == null)
                {
                    _categories = new CategoryRepository(_context);
                }
                return _categories;
            }
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void BeginTransaction()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("Transaction already started");
            }

            _transaction = _context.Database.BeginTransaction();
        }

        public void CommitTransaction()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction to commit");
            }

            try
            {
                _context.SaveChanges();
                _transaction.Commit();
            }
            catch
            {
                _transaction.Rollback();
                throw;
            }
            finally
            {
                _transaction.Dispose();
                _transaction = null;
            }
        }

        public void RollbackTransaction()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("No transaction to rollback");
            }

            _transaction.Rollback();
            _transaction.Dispose();
            _transaction = null;
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _transaction?.Dispose();
                    _context?.Dispose();
                }
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
