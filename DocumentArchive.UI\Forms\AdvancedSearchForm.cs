using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using DocumentArchive.UI.Helpers;

namespace DocumentArchive.UI.Forms
{
    public partial class AdvancedSearchForm : Form
    {
        private readonly IDocumentService _documentService;
        private readonly ICategoryService _categoryService;
        private readonly IExportService _exportService;
        private SearchCriteriaDto _searchCriteria;
        private List<DocumentDto> _searchResults;

        public event EventHandler<SearchResultsEventArgs> SearchCompleted;

        public AdvancedSearchForm(IDocumentService documentService, ICategoryService categoryService, IExportService exportService)
        {
            _documentService = documentService ?? throw new ArgumentNullException(nameof(documentService));
            _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
            _exportService = exportService ?? throw new ArgumentNullException(nameof(exportService));
            
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Configure form for Arabic
            UIHelper.ConfigureFormForArabic(this);
            
            // Style buttons
            UIHelper.StylePrimaryButton(searchButton);
            UIHelper.StyleSecondaryButton(clearButton);
            UIHelper.StyleSecondaryButton(exportButton);
            UIHelper.StyleSecondaryButton(closeButton);
            UIHelper.StyleWarningButton(saveSearchButton);
            
            // Style controls
            UIHelper.StyleTextBox(titleTextBox);
            UIHelper.StyleTextBox(documentNumberTextBox);
            UIHelper.StyleTextBox(senderRecipientTextBox);
            UIHelper.StyleTextBox(descriptionTextBox);
            UIHelper.StyleComboBox(categoryComboBox);
            UIHelper.StyleComboBox(statusComboBox);
            UIHelper.StyleComboBox(typeComboBox);
            UIHelper.StyleComboBox(sortColumnComboBox);
            UIHelper.StyleComboBox(pageSizeComboBox);
            UIHelper.StyleDataGridView(resultsDataGridView);
            
            // Initialize combo boxes
            InitializeComboBoxes();
            
            // Initialize search criteria
            _searchCriteria = new SearchCriteriaDto
            {
                PageNumber = 1,
                PageSize = 50,
                SortColumn = "Date",
                SortDescending = true
            };
            
            // Set default values
            SetDefaultValues();
        }

        private void InitializeComboBoxes()
        {
            // Document type combo box
            typeComboBox.Items.Clear();
            typeComboBox.Items.Add(new { Value = (DocumentType?)null, Text = "جميع الأنواع" });
            typeComboBox.Items.Add(new { Value = (DocumentType?)DocumentType.Incoming, Text = "وارد" });
            typeComboBox.Items.Add(new { Value = (DocumentType?)DocumentType.Outgoing, Text = "صادر" });
            typeComboBox.DisplayMember = "Text";
            typeComboBox.ValueMember = "Value";
            typeComboBox.SelectedIndex = 0;
            
            // Status combo box
            statusComboBox.Items.Clear();
            statusComboBox.Items.Add(new { Value = (DocumentStatus?)null, Text = "جميع الحالات" });
            statusComboBox.Items.Add(new { Value = (DocumentStatus?)DocumentStatus.Draft, Text = "مسودة" });
            statusComboBox.Items.Add(new { Value = (DocumentStatus?)DocumentStatus.Processed, Text = "معالج" });
            statusComboBox.Items.Add(new { Value = (DocumentStatus?)DocumentStatus.Archived, Text = "مؤرشف" });
            statusComboBox.DisplayMember = "Text";
            statusComboBox.ValueMember = "Value";
            statusComboBox.SelectedIndex = 0;
            
            // Sort column combo box
            sortColumnComboBox.Items.Clear();
            sortColumnComboBox.Items.Add(new { Value = "Date", Text = "التاريخ" });
            sortColumnComboBox.Items.Add(new { Value = "Title", Text = "العنوان" });
            sortColumnComboBox.Items.Add(new { Value = "DocumentNumber", Text = "رقم الوثيقة" });
            sortColumnComboBox.Items.Add(new { Value = "CreatedDate", Text = "تاريخ الإنشاء" });
            sortColumnComboBox.Items.Add(new { Value = "ModifiedDate", Text = "تاريخ التعديل" });
            sortColumnComboBox.DisplayMember = "Text";
            sortColumnComboBox.ValueMember = "Value";
            sortColumnComboBox.SelectedIndex = 0;
            
            // Page size combo box
            pageSizeComboBox.Items.AddRange(new object[] { "25", "50", "100", "200", "500" });
            pageSizeComboBox.SelectedItem = "50";
        }

        private void SetDefaultValues()
        {
            // Set date range to last month
            fromDatePicker.Value = DateTime.Now.AddMonths(-1);
            toDatePicker.Value = DateTime.Now;
            
            // Uncheck date filters by default
            fromDatePicker.Checked = false;
            toDatePicker.Checked = false;
            
            // Set sort order
            descendingRadioButton.Checked = true;
        }

        private async void AdvancedSearchForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadCategoriesAsync();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الفئات");
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                var categories = await _categoryService.GetActiveAsync();
                
                categoryComboBox.Items.Clear();
                categoryComboBox.Items.Add(new { Value = (int?)null, Text = "جميع الفئات" });
                
                foreach (var category in categories)
                {
                    categoryComboBox.Items.Add(new { Value = (int?)category.Id, Text = category.Name });
                }
                
                categoryComboBox.DisplayMember = "Text";
                categoryComboBox.ValueMember = "Value";
                categoryComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الفئات");
            }
        }

        private async void searchButton_Click(object sender, EventArgs e)
        {
            await PerformSearchAsync();
        }

        private async Task PerformSearchAsync()
        {
            try
            {
                // Build search criteria
                BuildSearchCriteria();
                
                var loadingForm = MessageHelper.ShowLoadingMessage("جاري البحث...");
                
                try
                {
                    var results = await _documentService.GetAllAsync(_searchCriteria);
                    _searchResults = results.Items;
                    
                    // Display results
                    DisplaySearchResults(results);
                    
                    // Update status
                    UpdateSearchStatus(results);
                    
                    // Enable export if there are results
                    exportButton.Enabled = _searchResults.Count > 0;
                    
                    // Notify parent form
                    SearchCompleted?.Invoke(this, new SearchResultsEventArgs(_searchCriteria, _searchResults));
                }
                finally
                {
                    MessageHelper.CloseLoadingMessage(loadingForm);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تنفيذ البحث");
            }
        }

        private void BuildSearchCriteria()
        {
            _searchCriteria = new SearchCriteriaDto
            {
                SearchText = titleTextBox.Text.Trim(),
                DocumentNumber = documentNumberTextBox.Text.Trim(),
                SenderRecipient = senderRecipientTextBox.Text.Trim(),
                Description = descriptionTextBox.Text.Trim(),
                
                DateFrom = fromDatePicker.Checked ? (DateTime?)fromDatePicker.Value.Date : null,
                DateTo = toDatePicker.Checked ? (DateTime?)toDatePicker.Value.Date : null,
                
                DocumentType = ((dynamic)typeComboBox.SelectedItem)?.Value,
                Status = ((dynamic)statusComboBox.SelectedItem)?.Value,
                
                PageNumber = 1,
                PageSize = int.Parse(pageSizeComboBox.SelectedItem.ToString()),
                SortColumn = ((dynamic)sortColumnComboBox.SelectedItem)?.Value?.ToString() ?? "Date",
                SortDescending = descendingRadioButton.Checked
            };
            
            // Category filter
            var selectedCategory = ((dynamic)categoryComboBox.SelectedItem)?.Value;
            if (selectedCategory.HasValue)
            {
                _searchCriteria.CategoryIds = new List<int> { selectedCategory.Value };
            }
            else
            {
                _searchCriteria.CategoryIds.Clear();
            }
            
            // File filter
            if (hasFileCheckBox.Checked)
            {
                _searchCriteria.HasFile = true;
            }
            else if (noFileCheckBox.Checked)
            {
                _searchCriteria.HasFile = false;
            }
            else
            {
                _searchCriteria.HasFile = null;
            }
        }

        private void DisplaySearchResults(PagedResult<DocumentDto> results)
        {
            resultsDataGridView.DataSource = results.Items;
            ConfigureResultsGrid();
        }

        private void ConfigureResultsGrid()
        {
            if (resultsDataGridView.Columns.Count == 0) return;

            // Hide unnecessary columns
            if (resultsDataGridView.Columns["Id"] != null)
                resultsDataGridView.Columns["Id"].Visible = false;
            
            if (resultsDataGridView.Columns["CategoryId"] != null)
                resultsDataGridView.Columns["CategoryId"].Visible = false;
            
            if (resultsDataGridView.Columns["FilePath"] != null)
                resultsDataGridView.Columns["FilePath"].Visible = false;

            // Configure visible columns
            var columnConfig = new Dictionary<string, (string Header, int Width)>
            {
                { "DocumentNumber", ("رقم الوثيقة", 120) },
                { "Title", ("العنوان", 200) },
                { "Date", ("التاريخ", 100) },
                { "TypeDisplayName", ("النوع", 80) },
                { "SenderRecipient", ("المرسل/المستقبل", 150) },
                { "CategoryName", ("الفئة", 120) },
                { "StatusDisplayName", ("الحالة", 80) },
                { "CreatedDate", ("تاريخ الإنشاء", 120) }
            };

            foreach (var config in columnConfig)
            {
                if (resultsDataGridView.Columns[config.Key] != null)
                {
                    var column = resultsDataGridView.Columns[config.Key];
                    column.HeaderText = config.Value.Header;
                    column.Width = config.Value.Width;
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                }
            }

            // Format date columns
            if (resultsDataGridView.Columns["Date"] != null)
            {
                resultsDataGridView.Columns["Date"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
            
            if (resultsDataGridView.Columns["CreatedDate"] != null)
            {
                resultsDataGridView.Columns["CreatedDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm";
            }
        }

        private void UpdateSearchStatus(PagedResult<DocumentDto> results)
        {
            statusLabel.Text = $"تم العثور على {results.TotalCount} وثيقة";
            
            if (results.TotalCount > results.Items.Count)
            {
                statusLabel.Text += $" (عرض {results.Items.Count} من {results.TotalCount})";
            }
        }

        private void clearButton_Click(object sender, EventArgs e)
        {
            ClearSearchForm();
        }

        private void ClearSearchForm()
        {
            titleTextBox.Clear();
            documentNumberTextBox.Clear();
            senderRecipientTextBox.Clear();
            descriptionTextBox.Clear();
            
            fromDatePicker.Checked = false;
            toDatePicker.Checked = false;
            
            typeComboBox.SelectedIndex = 0;
            statusComboBox.SelectedIndex = 0;
            categoryComboBox.SelectedIndex = 0;
            
            hasFileCheckBox.Checked = false;
            noFileCheckBox.Checked = false;
            
            sortColumnComboBox.SelectedIndex = 0;
            descendingRadioButton.Checked = true;
            pageSizeComboBox.SelectedItem = "50";
            
            resultsDataGridView.DataSource = null;
            statusLabel.Text = "أدخل معايير البحث واضغط 'بحث'";
            exportButton.Enabled = false;
        }

        private async void exportButton_Click(object sender, EventArgs e)
        {
            if (_searchResults == null || _searchResults.Count == 0)
            {
                MessageHelper.ShowWarning("لا توجد نتائج للتصدير");
                return;
            }

            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Title = "تصدير نتائج البحث";
                    saveDialog.Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv|Text Files (*.txt)|*.txt";
                    saveDialog.FileName = $"نتائج_البحث_{DateTime.Now:yyyyMMdd_HHmmss}";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        var loadingForm = MessageHelper.ShowLoadingMessage("جاري تصدير البيانات...");
                        
                        try
                        {
                            byte[] data;
                            var extension = System.IO.Path.GetExtension(saveDialog.FileName).ToLower();
                            
                            switch (extension)
                            {
                                case ".xlsx":
                                    data = await _exportService.ExportSearchResultsToExcelAsync(_searchCriteria, _searchResults);
                                    break;
                                case ".csv":
                                    data = await _exportService.ExportDocumentsToCsvAsync(_searchResults);
                                    break;
                                case ".txt":
                                    data = await _exportService.GeneratePdfReportAsync(_searchResults, "نتائج البحث المتقدم", "detailed");
                                    break;
                                default:
                                    data = await _exportService.ExportDocumentsToCsvAsync(_searchResults);
                                    break;
                            }
                            
                            await System.IO.File.WriteAllBytesAsync(saveDialog.FileName, data);
                            MessageHelper.ShowSuccess("تم تصدير البيانات بنجاح");
                        }
                        finally
                        {
                            MessageHelper.CloseLoadingMessage(loadingForm);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تصدير البيانات");
            }
        }

        private void saveSearchButton_Click(object sender, EventArgs e)
        {
            // TODO: Implement save search functionality
            MessageHelper.ShowInformation("ميزة حفظ البحث ستكون متاحة قريباً");
        }

        private void closeButton_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void resultsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var selectedDocument = (DocumentDto)resultsDataGridView.Rows[e.RowIndex].DataBoundItem;
                
                // Open document viewer
                try
                {
                    var viewerForm = Program.GetService<DocumentViewerForm>();
                    _ = viewerForm.LoadDocumentAsync(selectedDocument.Id);
                    viewerForm.Show();
                }
                catch (Exception ex)
                {
                    MessageHelper.ShowException(ex, "فشل في فتح معاينة الوثيقة");
                }
            }
        }

        private void hasFileCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            if (hasFileCheckBox.Checked)
            {
                noFileCheckBox.Checked = false;
            }
        }

        private void noFileCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            if (noFileCheckBox.Checked)
            {
                hasFileCheckBox.Checked = false;
            }
        }
    }

    public class SearchResultsEventArgs : EventArgs
    {
        public SearchCriteriaDto Criteria { get; }
        public List<DocumentDto> Results { get; }

        public SearchResultsEventArgs(SearchCriteriaDto criteria, List<DocumentDto> results)
        {
            Criteria = criteria;
            Results = results;
        }
    }
}
