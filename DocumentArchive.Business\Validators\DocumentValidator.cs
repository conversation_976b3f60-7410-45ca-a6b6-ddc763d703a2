using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using DocumentArchive.Business.Helpers;
using DocumentArchive.Data.Interfaces;
using DocumentArchive.Models.DTOs;

namespace DocumentArchive.Business.Validators
{
    /// <summary>
    /// Validator for Document operations
    /// </summary>
    public class DocumentValidator
    {
        private readonly IUnitOfWork _unitOfWork;

        public DocumentValidator(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        /// <summary>
        /// Validate document data
        /// </summary>
        public async Task<List<string>> ValidateAsync(DocumentDto documentDto, bool isUpdate = false)
        {
            var errors = new List<string>();

            // Required field validations
            if (string.IsNullOrWhiteSpace(documentDto.Title))
            {
                errors.Add("عنوان الوثيقة مطلوب");
            }
            else if (documentDto.Title.Length > 200)
            {
                errors.Add("عنوان الوثيقة لا يمكن أن يتجاوز 200 حرف");
            }

            if (documentDto.Date == default(DateTime))
            {
                errors.Add("تاريخ الوثيقة مطلوب");
            }
            else if (documentDto.Date > DateTime.Now.Date)
            {
                errors.Add("تاريخ الوثيقة لا يمكن أن يكون في المستقبل");
            }

            if (string.IsNullOrWhiteSpace(documentDto.SenderRecipient))
            {
                errors.Add("المرسل/المستقبل مطلوب");
            }
            else if (documentDto.SenderRecipient.Length > 200)
            {
                errors.Add("المرسل/المستقبل لا يمكن أن يتجاوز 200 حرف");
            }

            if (documentDto.CategoryId <= 0)
            {
                errors.Add("فئة الوثيقة مطلوبة");
            }
            else
            {
                // Check if category exists and is active
                var category = await _unitOfWork.Categories.GetByIdAsync(documentDto.CategoryId);
                if (category == null)
                {
                    errors.Add("الفئة المحددة غير موجودة");
                }
                else if (!category.IsActive)
                {
                    errors.Add("الفئة المحددة غير نشطة");
                }
            }

            // Optional field validations
            if (!string.IsNullOrWhiteSpace(documentDto.Description) && documentDto.Description.Length > 1000)
            {
                errors.Add("وصف الوثيقة لا يمكن أن يتجاوز 1000 حرف");
            }

            // Document number validation
            if (!isUpdate && !string.IsNullOrWhiteSpace(documentDto.DocumentNumber))
            {
                if (!NumberingHelper.IsValidDocumentNumber(documentDto.DocumentNumber))
                {
                    errors.Add("تنسيق رقم الوثيقة غير صحيح");
                }
                else
                {
                    var exists = await _unitOfWork.Documents.DocumentNumberExistsAsync(documentDto.DocumentNumber);
                    if (exists)
                    {
                        errors.Add("رقم الوثيقة موجود مسبقاً");
                    }
                }
            }

            // File validation
            if (!string.IsNullOrWhiteSpace(documentDto.FilePath))
            {
                if (!File.Exists(documentDto.FilePath))
                {
                    errors.Add("الملف المحدد غير موجود");
                }
                else
                {
                    var fileInfo = new FileInfo(documentDto.FilePath);
                    
                    // Check file size (max 50MB)
                    if (fileInfo.Length > 50 * 1024 * 1024)
                    {
                        errors.Add("حجم الملف لا يمكن أن يتجاوز 50 ميجابايت");
                    }

                    // Check file type
                    if (!FileHelper.IsValidFileType(documentDto.OriginalFileName ?? fileInfo.Name))
                    {
                        errors.Add("نوع الملف غير مدعوم");
                    }
                }
            }

            return errors;
        }

        /// <summary>
        /// Validate file for upload
        /// </summary>
        public List<string> ValidateFile(string filePath, string originalFileName = null)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(filePath))
            {
                errors.Add("مسار الملف مطلوب");
                return errors;
            }

            if (!File.Exists(filePath))
            {
                errors.Add("الملف غير موجود");
                return errors;
            }

            var fileInfo = new FileInfo(filePath);
            var fileName = originalFileName ?? fileInfo.Name;

            // Check file size (max 50MB)
            if (fileInfo.Length > 50 * 1024 * 1024)
            {
                errors.Add("حجم الملف لا يمكن أن يتجاوز 50 ميجابايت");
            }

            // Check if file is empty
            if (fileInfo.Length == 0)
            {
                errors.Add("الملف فارغ");
            }

            // Check file type
            if (!FileHelper.IsValidFileType(fileName))
            {
                var supportedTypes = string.Join(", ", FileHelper.GetSupportedExtensions());
                errors.Add($"نوع الملف غير مدعوم. الأنواع المدعومة: {supportedTypes}");
            }

            return errors;
        }

        /// <summary>
        /// Validate status change
        /// </summary>
        public List<string> ValidateStatusChange(DocumentDto document, Models.Enums.DocumentStatus newStatus)
        {
            var errors = new List<string>();

            if (document == null)
            {
                errors.Add("الوثيقة غير موجودة");
                return errors;
            }

            if (!NumberingHelper.IsStatusTransitionAllowed(document.Status, newStatus))
            {
                errors.Add($"لا يمكن تغيير حالة الوثيقة من {NumberingHelper.GetDocumentStatusDisplayName(document.Status)} إلى {NumberingHelper.GetDocumentStatusDisplayName(newStatus)}");
            }

            return errors;
        }
    }
}
