using System.Collections.Generic;
using System.Threading.Tasks;
using DocumentArchive.Models.DTOs;

namespace DocumentArchive.Business.Interfaces
{
    /// <summary>
    /// Service interface for Category operations
    /// </summary>
    public interface ICategoryService
    {
        /// <summary>
        /// Get category by ID
        /// </summary>
        Task<CategoryDto> GetByIdAsync(int id);

        /// <summary>
        /// Get all categories
        /// </summary>
        Task<List<CategoryDto>> GetAllAsync();

        /// <summary>
        /// Get active categories only
        /// </summary>
        Task<List<CategoryDto>> GetActiveAsync();

        /// <summary>
        /// Create new category
        /// </summary>
        Task<CategoryDto> CreateAsync(CategoryDto categoryDto);

        /// <summary>
        /// Update existing category
        /// </summary>
        Task<CategoryDto> UpdateAsync(CategoryDto categoryDto);

        /// <summary>
        /// Delete category
        /// </summary>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Search categories by name
        /// </summary>
        Task<List<CategoryDto>> SearchAsync(string searchText);

        /// <summary>
        /// Validate category data
        /// </summary>
        Task<List<string>> ValidateCategoryAsync(CategoryDto categoryDto);

        /// <summary>
        /// Get category with document count
        /// </summary>
        Task<CategoryDto> GetWithDocumentCountAsync(int id);

        /// <summary>
        /// Check if category can be deleted
        /// </summary>
        Task<bool> CanDeleteAsync(int id);
    }
}
