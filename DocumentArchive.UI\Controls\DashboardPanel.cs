using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using DocumentArchive.UI.Helpers;

namespace DocumentArchive.UI.Controls
{
    public partial class DashboardPanel : UserControl
    {
        private readonly IDocumentService _documentService;
        private readonly ICategoryService _categoryService;
        private Timer _refreshTimer;

        public DashboardPanel()
        {
            InitializeComponent();
        }

        public DashboardPanel(IDocumentService documentService, ICategoryService categoryService)
        {
            _documentService = documentService ?? throw new ArgumentNullException(nameof(documentService));
            _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
            
            InitializeComponent();
            InitializePanel();
        }

        private void InitializePanel()
        {
            // Configure for Arabic
            UIHelper.ConfigureControlsForArabic(this);
            
            // Style the panel
            BackColor = Color.White;
            Padding = new Padding(16);
            
            // Initialize refresh timer (refresh every 5 minutes)
            _refreshTimer = new Timer
            {
                Interval = 5 * 60 * 1000, // 5 minutes
                Enabled = true
            };
            _refreshTimer.Tick += async (s, e) => await RefreshDataAsync();
        }

        public async Task LoadDashboardAsync()
        {
            try
            {
                await RefreshDataAsync();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل لوحة المعلومات");
            }
        }

        private async Task RefreshDataAsync()
        {
            try
            {
                // Update last refresh time
                lastRefreshLabel.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
                
                // Load statistics
                await LoadStatisticsAsync();
                
                // Load recent documents
                await LoadRecentDocumentsAsync();
                
                // Load category statistics
                await LoadCategoryStatsAsync();
                
                // Load quick stats
                await LoadQuickStatsAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't show to user for background refresh
                System.Diagnostics.Debug.WriteLine($"Dashboard refresh error: {ex.Message}");
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var statistics = await _documentService.GetStatisticsAsync();
                
                // Update main statistics cards
                totalDocumentsCard.UpdateValue(statistics.GetValueOrDefault("TotalDocuments", 0));
                incomingDocumentsCard.UpdateValue(statistics.GetValueOrDefault("IncomingDocuments", 0));
                outgoingDocumentsCard.UpdateValue(statistics.GetValueOrDefault("OutgoingDocuments", 0));
                
                // Update status statistics
                draftDocumentsCard.UpdateValue(statistics.GetValueOrDefault("DraftDocuments", 0));
                processedDocumentsCard.UpdateValue(statistics.GetValueOrDefault("ProcessedDocuments", 0));
                archivedDocumentsCard.UpdateValue(statistics.GetValueOrDefault("ArchivedDocuments", 0));
                
                // Update progress bars
                UpdateProgressBars(statistics);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
            }
        }

        private void UpdateProgressBars(Dictionary<string, int> statistics)
        {
            var total = statistics.GetValueOrDefault("TotalDocuments", 0);
            
            if (total > 0)
            {
                var incoming = statistics.GetValueOrDefault("IncomingDocuments", 0);
                var outgoing = statistics.GetValueOrDefault("OutgoingDocuments", 0);
                var draft = statistics.GetValueOrDefault("DraftDocuments", 0);
                var processed = statistics.GetValueOrDefault("ProcessedDocuments", 0);
                var archived = statistics.GetValueOrDefault("ArchivedDocuments", 0);
                
                // Type distribution
                incomingProgressBar.Value = Math.Min((incoming * 100) / total, 100);
                outgoingProgressBar.Value = Math.Min((outgoing * 100) / total, 100);
                
                // Status distribution
                draftProgressBar.Value = Math.Min((draft * 100) / total, 100);
                processedProgressBar.Value = Math.Min((processed * 100) / total, 100);
                archivedProgressBar.Value = Math.Min((archived * 100) / total, 100);
                
                // Update percentage labels
                incomingPercentLabel.Text = $"{(incoming * 100) / total}%";
                outgoingPercentLabel.Text = $"{(outgoing * 100) / total}%";
                draftPercentLabel.Text = $"{(draft * 100) / total}%";
                processedPercentLabel.Text = $"{(processed * 100) / total}%";
                archivedPercentLabel.Text = $"{(archived * 100) / total}%";
            }
            else
            {
                // Reset all progress bars
                incomingProgressBar.Value = 0;
                outgoingProgressBar.Value = 0;
                draftProgressBar.Value = 0;
                processedProgressBar.Value = 0;
                archivedProgressBar.Value = 0;
                
                incomingPercentLabel.Text = "0%";
                outgoingPercentLabel.Text = "0%";
                draftPercentLabel.Text = "0%";
                processedPercentLabel.Text = "0%";
                archivedPercentLabel.Text = "0%";
            }
        }

        private async Task LoadRecentDocumentsAsync()
        {
            try
            {
                var criteria = new SearchCriteriaDto
                {
                    PageSize = 10,
                    PageNumber = 1,
                    SortColumn = "CreatedDate",
                    SortDescending = true
                };
                
                var recentDocuments = await _documentService.GetAllAsync(criteria);
                
                // Update recent documents list
                recentDocumentsListBox.Items.Clear();
                
                foreach (var doc in recentDocuments.Items.Take(10))
                {
                    var displayText = $"{doc.DocumentNumber} - {doc.Title}";
                    if (displayText.Length > 50)
                    {
                        displayText = displayText.Substring(0, 47) + "...";
                    }
                    
                    recentDocumentsListBox.Items.Add(new
                    {
                        Text = displayText,
                        Document = doc
                    });
                }
                
                recentDocumentsListBox.DisplayMember = "Text";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading recent documents: {ex.Message}");
            }
        }

        private async Task LoadCategoryStatsAsync()
        {
            try
            {
                var categories = await _categoryService.GetAllAsync();
                
                // Update category statistics chart
                categoryStatsListBox.Items.Clear();
                
                var topCategories = categories
                    .Where(c => c.DocumentCount > 0)
                    .OrderByDescending(c => c.DocumentCount)
                    .Take(8);
                
                foreach (var category in topCategories)
                {
                    var displayText = $"{category.Name}: {category.DocumentCount} وثيقة";
                    categoryStatsListBox.Items.Add(displayText);
                }
                
                if (!topCategories.Any())
                {
                    categoryStatsListBox.Items.Add("لا توجد فئات بوثائق");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading category stats: {ex.Message}");
            }
        }

        private async Task LoadQuickStatsAsync()
        {
            try
            {
                // Today's documents
                var todayCriteria = new SearchCriteriaDto
                {
                    DateFrom = DateTime.Today,
                    DateTo = DateTime.Today.AddDays(1),
                    PageSize = int.MaxValue,
                    PageNumber = 1
                };
                
                var todayDocs = await _documentService.GetAllAsync(todayCriteria);
                todayDocumentsLabel.Text = todayDocs.TotalCount.ToString();
                
                // This week's documents
                var weekStart = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                var weekCriteria = new SearchCriteriaDto
                {
                    DateFrom = weekStart,
                    DateTo = DateTime.Today.AddDays(1),
                    PageSize = int.MaxValue,
                    PageNumber = 1
                };
                
                var weekDocs = await _documentService.GetAllAsync(weekCriteria);
                weekDocumentsLabel.Text = weekDocs.TotalCount.ToString();
                
                // This month's documents
                var monthStart = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                var monthCriteria = new SearchCriteriaDto
                {
                    DateFrom = monthStart,
                    DateTo = DateTime.Today.AddDays(1),
                    PageSize = int.MaxValue,
                    PageNumber = 1
                };
                
                var monthDocs = await _documentService.GetAllAsync(monthCriteria);
                monthDocumentsLabel.Text = monthDocs.TotalCount.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading quick stats: {ex.Message}");
            }
        }

        private void recentDocumentsListBox_DoubleClick(object sender, EventArgs e)
        {
            if (recentDocumentsListBox.SelectedItem != null)
            {
                var selectedItem = (dynamic)recentDocumentsListBox.SelectedItem;
                var document = (DocumentDto)selectedItem.Document;
                
                // Raise event to open document viewer
                DocumentSelected?.Invoke(this, new DocumentSelectedEventArgs(document.Id));
            }
        }

        private void refreshButton_Click(object sender, EventArgs e)
        {
            _ = RefreshDataAsync();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            if (!DesignMode && _documentService != null)
            {
                _ = LoadDashboardAsync();
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _refreshTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        // Events
        public event EventHandler<DocumentSelectedEventArgs> DocumentSelected;
    }

    public class DocumentSelectedEventArgs : EventArgs
    {
        public int DocumentId { get; }

        public DocumentSelectedEventArgs(int documentId)
        {
            DocumentId = documentId;
        }
    }

    // Helper class for statistics cards
    public class StatisticsCard : Panel
    {
        private Label _titleLabel;
        private Label _valueLabel;
        private Label _iconLabel;

        public StatisticsCard(string title, string icon, Color backgroundColor)
        {
            InitializeCard(title, icon, backgroundColor);
        }

        private void InitializeCard(string title, string icon, Color backgroundColor)
        {
            Size = new Size(200, 100);
            BackColor = backgroundColor;
            BorderStyle = BorderStyle.FixedSingle;
            Padding = new Padding(12);

            _iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Symbol", 24F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(12, 12),
                Size = new Size(40, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            _titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.White,
                Location = new Point(60, 12),
                Size = new Size(120, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            _valueLabel = new Label
            {
                Text = "0",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(60, 35),
                Size = new Size(120, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Controls.AddRange(new Control[] { _iconLabel, _titleLabel, _valueLabel });
        }

        public void UpdateValue(int value)
        {
            _valueLabel.Text = value.ToString("N0");
        }

        public void UpdateValue(string value)
        {
            _valueLabel.Text = value;
        }
    }
}
