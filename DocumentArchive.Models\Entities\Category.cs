using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DocumentArchive.Models.Entities
{
    /// <summary>
    /// Represents a document category
    /// </summary>
    [Table("Categories")]
    public class Category
    {
        /// <summary>
        /// Unique identifier for the category
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Name of the category
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// Description of the category
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// Indicates if the category is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Date when the category was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Date when the category was last modified
        /// </summary>
        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Navigation property for documents in this category
        /// </summary>
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
    }
}
