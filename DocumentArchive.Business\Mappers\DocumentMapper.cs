using System.Collections.Generic;
using System.Linq;
using DocumentArchive.Business.Helpers;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Entities;

namespace DocumentArchive.Business.Mappers
{
    /// <summary>
    /// Mapper for Document entity and DTO conversions
    /// </summary>
    public static class DocumentMapper
    {
        /// <summary>
        /// Convert Document entity to DocumentDto
        /// </summary>
        public static DocumentDto ToDto(Document document)
        {
            if (document == null) return null;

            return new DocumentDto
            {
                Id = document.Id,
                Title = document.Title,
                DocumentNumber = document.DocumentNumber,
                Date = document.Date,
                Type = document.Type,
                TypeDisplayName = NumberingHelper.GetDocumentTypeDisplayName(document.Type),
                SenderRecipient = document.SenderRecipient,
                CategoryId = document.CategoryId,
                CategoryName = document.Category?.Name,
                Description = document.Description,
                Status = document.Status,
                StatusDisplayName = NumberingHelper.GetDocumentStatusDisplayName(document.Status),
                FilePath = document.FilePath,
                OriginalFileName = document.OriginalFileName,
                FileSize = document.FileSize,
                FileSizeFormatted = document.FileSize.HasValue ? FileHelper.FormatFileSize(document.FileSize.Value) : string.Empty,
                MimeType = document.MimeType,
                CreatedDate = document.CreatedDate,
                ModifiedDate = document.ModifiedDate
            };
        }

        /// <summary>
        /// Convert DocumentDto to Document entity
        /// </summary>
        public static Document ToEntity(DocumentDto documentDto)
        {
            if (documentDto == null) return null;

            return new Document
            {
                Id = documentDto.Id,
                Title = documentDto.Title,
                DocumentNumber = documentDto.DocumentNumber,
                Date = documentDto.Date,
                Type = documentDto.Type,
                SenderRecipient = documentDto.SenderRecipient,
                CategoryId = documentDto.CategoryId,
                Description = documentDto.Description,
                Status = documentDto.Status,
                FilePath = documentDto.FilePath,
                OriginalFileName = documentDto.OriginalFileName,
                FileSize = documentDto.FileSize,
                MimeType = documentDto.MimeType,
                CreatedDate = documentDto.CreatedDate,
                ModifiedDate = documentDto.ModifiedDate
            };
        }

        /// <summary>
        /// Convert list of Document entities to list of DocumentDtos
        /// </summary>
        public static List<DocumentDto> ToDtoList(List<Document> documents)
        {
            if (documents == null) return new List<DocumentDto>();

            return documents.Select(ToDto).ToList();
        }

        /// <summary>
        /// Convert list of DocumentDtos to list of Document entities
        /// </summary>
        public static List<Document> ToEntityList(List<DocumentDto> documentDtos)
        {
            if (documentDtos == null) return new List<Document>();

            return documentDtos.Select(ToEntity).ToList();
        }

        /// <summary>
        /// Update Document entity from DocumentDto
        /// </summary>
        public static void UpdateEntityFromDto(Document entity, DocumentDto dto)
        {
            if (entity == null || dto == null) return;

            entity.Title = dto.Title;
            entity.Date = dto.Date;
            entity.Type = dto.Type;
            entity.SenderRecipient = dto.SenderRecipient;
            entity.CategoryId = dto.CategoryId;
            entity.Description = dto.Description;
            entity.Status = dto.Status;
            
            // Only update file-related properties if they are provided
            if (!string.IsNullOrEmpty(dto.FilePath))
            {
                entity.FilePath = dto.FilePath;
                entity.OriginalFileName = dto.OriginalFileName;
                entity.FileSize = dto.FileSize;
                entity.MimeType = dto.MimeType;
            }
        }

        /// <summary>
        /// Convert PagedResult of Document entities to PagedResult of DocumentDtos
        /// </summary>
        public static PagedResult<DocumentDto> ToPagedDto(PagedResult<Document> pagedDocuments)
        {
            if (pagedDocuments == null) return new PagedResult<DocumentDto>();

            return new PagedResult<DocumentDto>
            {
                Items = ToDtoList(pagedDocuments.Items),
                TotalCount = pagedDocuments.TotalCount,
                PageNumber = pagedDocuments.PageNumber,
                PageSize = pagedDocuments.PageSize
            };
        }
    }
}
