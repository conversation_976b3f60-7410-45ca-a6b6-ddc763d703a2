using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace DocumentArchive.Business.Helpers
{
    /// <summary>
    /// Helper class for file operations
    /// </summary>
    public static class FileHelper
    {
        private static readonly Dictionary<string, string> MimeTypes = new Dictionary<string, string>
        {
            { ".pdf", "application/pdf" },
            { ".doc", "application/msword" },
            { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
            { ".xls", "application/vnd.ms-excel" },
            { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
            { ".jpg", "image/jpeg" },
            { ".jpeg", "image/jpeg" },
            { ".png", "image/png" },
            { ".txt", "text/plain" }
        };

        private static readonly List<string> SupportedExtensions = new List<string>
        {
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".txt"
        };

        /// <summary>
        /// Get MIME type for file extension
        /// </summary>
        public static string GetMimeType(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return "application/octet-stream";

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return MimeTypes.ContainsKey(extension) ? MimeTypes[extension] : "application/octet-stream";
        }

        /// <summary>
        /// Check if file type is supported
        /// </summary>
        public static bool IsValidFileType(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return false;

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return SupportedExtensions.Contains(extension);
        }

        /// <summary>
        /// Get list of supported file extensions
        /// </summary>
        public static List<string> GetSupportedExtensions()
        {
            return new List<string>(SupportedExtensions);
        }

        /// <summary>
        /// Format file size for display
        /// </summary>
        public static string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size = size / 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Generate safe file name
        /// </summary>
        public static string GenerateSafeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return "document";

            var invalidChars = Path.GetInvalidFileNameChars();
            var safeName = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            
            if (string.IsNullOrEmpty(safeName))
            {
                safeName = "document";
            }

            return safeName;
        }

        /// <summary>
        /// Generate unique file name to avoid conflicts
        /// </summary>
        public static string GenerateUniqueFileName(string directory, string fileName)
        {
            if (!Directory.Exists(directory)) return fileName;

            var name = Path.GetFileNameWithoutExtension(fileName);
            var extension = Path.GetExtension(fileName);
            var counter = 1;
            var newFileName = fileName;

            while (File.Exists(Path.Combine(directory, newFileName)))
            {
                newFileName = $"{name}_{counter}{extension}";
                counter++;
            }

            return newFileName;
        }

        /// <summary>
        /// Check if file is an image
        /// </summary>
        public static bool IsImageFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return false;

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension == ".jpg" || extension == ".jpeg" || extension == ".png";
        }

        /// <summary>
        /// Check if file is a document
        /// </summary>
        public static bool IsDocumentFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return false;

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension == ".pdf" || extension == ".doc" || extension == ".docx" || 
                   extension == ".xls" || extension == ".xlsx" || extension == ".txt";
        }

        /// <summary>
        /// Create directory if it doesn't exist
        /// </summary>
        public static void EnsureDirectoryExists(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }

        /// <summary>
        /// Get file extension filter for OpenFileDialog
        /// </summary>
        public static string GetFileFilter()
        {
            return "All Supported Files|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.jpg;*.jpeg;*.png;*.txt|" +
                   "PDF Files|*.pdf|" +
                   "Word Documents|*.doc;*.docx|" +
                   "Excel Files|*.xls;*.xlsx|" +
                   "Image Files|*.jpg;*.jpeg;*.png|" +
                   "Text Files|*.txt|" +
                   "All Files|*.*";
        }
    }
}
