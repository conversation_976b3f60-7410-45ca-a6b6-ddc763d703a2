using System;
using DocumentArchive.Business.Helpers;
using DocumentArchive.Models.Enums;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DocumentArchive.Tests.Helpers
{
    [TestClass]
    public class NumberingHelperTests
    {
        [TestMethod]
        public void GetDocumentPrefix_IncomingType_ReturnsIN()
        {
            // Arrange
            var documentType = DocumentType.Incoming;

            // Act
            var result = NumberingHelper.GetDocumentPrefix(documentType);

            // Assert
            Assert.AreEqual("IN", result);
        }

        [TestMethod]
        public void GetDocumentPrefix_OutgoingType_ReturnsOUT()
        {
            // Arrange
            var documentType = DocumentType.Outgoing;

            // Act
            var result = NumberingHelper.GetDocumentPrefix(documentType);

            // Assert
            Assert.AreEqual("OUT", result);
        }

        [TestMethod]
        public void ParseDocumentNumber_ValidIncomingNumber_ReturnsCorrectComponents()
        {
            // Arrange
            var documentNumber = "IN-2024-0001";

            // Act
            var (type, year, number) = NumberingHelper.ParseDocumentNumber(documentNumber);

            // Assert
            Assert.AreEqual(DocumentType.Incoming, type);
            Assert.AreEqual(2024, year);
            Assert.AreEqual(1, number);
        }

        [TestMethod]
        public void ParseDocumentNumber_ValidOutgoingNumber_ReturnsCorrectComponents()
        {
            // Arrange
            var documentNumber = "OUT-2024-0123";

            // Act
            var (type, year, number) = NumberingHelper.ParseDocumentNumber(documentNumber);

            // Assert
            Assert.AreEqual(DocumentType.Outgoing, type);
            Assert.AreEqual(2024, year);
            Assert.AreEqual(123, number);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void ParseDocumentNumber_InvalidFormat_ThrowsException()
        {
            // Arrange
            var documentNumber = "INVALID-FORMAT";

            // Act
            NumberingHelper.ParseDocumentNumber(documentNumber);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void ParseDocumentNumber_NullOrEmpty_ThrowsException()
        {
            // Act
            NumberingHelper.ParseDocumentNumber(null);
        }

        [TestMethod]
        public void IsValidDocumentNumber_ValidNumber_ReturnsTrue()
        {
            // Arrange
            var documentNumber = "IN-2024-0001";

            // Act
            var result = NumberingHelper.IsValidDocumentNumber(documentNumber);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsValidDocumentNumber_InvalidNumber_ReturnsFalse()
        {
            // Arrange
            var documentNumber = "INVALID";

            // Act
            var result = NumberingHelper.IsValidDocumentNumber(documentNumber);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void GenerateDocumentNumber_ValidInputs_ReturnsFormattedNumber()
        {
            // Arrange
            var type = DocumentType.Incoming;
            var year = 2024;
            var nextNumber = 1;

            // Act
            var result = NumberingHelper.GenerateDocumentNumber(type, year, nextNumber);

            // Assert
            Assert.AreEqual("IN-2024-0001", result);
        }

        [TestMethod]
        public void GetDocumentTypeDisplayName_IncomingType_ReturnsArabicName()
        {
            // Arrange
            var type = DocumentType.Incoming;

            // Act
            var result = NumberingHelper.GetDocumentTypeDisplayName(type);

            // Assert
            Assert.AreEqual("وارد", result);
        }

        [TestMethod]
        public void GetDocumentTypeDisplayName_OutgoingType_ReturnsArabicName()
        {
            // Arrange
            var type = DocumentType.Outgoing;

            // Act
            var result = NumberingHelper.GetDocumentTypeDisplayName(type);

            // Assert
            Assert.AreEqual("صادر", result);
        }

        [TestMethod]
        public void GetDocumentStatusDisplayName_DraftStatus_ReturnsArabicName()
        {
            // Arrange
            var status = DocumentStatus.Draft;

            // Act
            var result = NumberingHelper.GetDocumentStatusDisplayName(status);

            // Assert
            Assert.AreEqual("مسودة", result);
        }

        [TestMethod]
        public void IsStatusTransitionAllowed_DraftToProcessed_ReturnsTrue()
        {
            // Arrange
            var fromStatus = DocumentStatus.Draft;
            var toStatus = DocumentStatus.Processed;

            // Act
            var result = NumberingHelper.IsStatusTransitionAllowed(fromStatus, toStatus);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsStatusTransitionAllowed_ProcessedToDraft_ReturnsTrue()
        {
            // Arrange
            var fromStatus = DocumentStatus.Processed;
            var toStatus = DocumentStatus.Draft;

            // Act
            var result = NumberingHelper.IsStatusTransitionAllowed(fromStatus, toStatus);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsStatusTransitionAllowed_DeletedToAny_ReturnsFalse()
        {
            // Arrange
            var fromStatus = DocumentStatus.Deleted;
            var toStatus = DocumentStatus.Draft;

            // Act
            var result = NumberingHelper.IsStatusTransitionAllowed(fromStatus, toStatus);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsStatusTransitionAllowed_AnyToDeleted_ReturnsTrue()
        {
            // Arrange
            var fromStatus = DocumentStatus.Processed;
            var toStatus = DocumentStatus.Deleted;

            // Act
            var result = NumberingHelper.IsStatusTransitionAllowed(fromStatus, toStatus);

            // Assert
            Assert.IsTrue(result);
        }
    }
}
