using System;

namespace DocumentArchive.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for Category
    /// </summary>
    public class CategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public int DocumentCount { get; set; }
    }
}
