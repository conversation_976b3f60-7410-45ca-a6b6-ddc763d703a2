<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  
  <targets>
    <!-- File target -->
    <target xsi:type="File" name="fileTarget"
            fileName="Logs\DocumentArchive-${shortdate}.log"
            layout="${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="Logs\Archive\DocumentArchive-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- Console target -->
    <target xsi:type="Console" name="consoleTarget"
            layout="${time} [${uppercase:${level}}] ${message} ${exception:format=tostring}" />
    
    <!-- Debug output target -->
    <target xsi:type="Debugger" name="debugTarget"
            layout="${time} [${uppercase:${level}}] ${logger:shortName=true} ${message}" />
  </targets>
  
  <rules>
    <!-- All logs to file -->
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
    
    <!-- Debug and above to console (only in debug mode) -->
    <logger name="*" minlevel="Debug" writeTo="consoleTarget" />
    
    <!-- Debug output -->
    <logger name="*" minlevel="Debug" writeTo="debugTarget" />
  </rules>
</nlog>
