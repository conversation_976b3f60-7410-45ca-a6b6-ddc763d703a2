<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ApplicationTitle" xml:space="preserve">
    <value>نظام أرشفة الوثائق الإلكترونية</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>تحديث</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>إغلاق</value>
  </data>
  <data name="IncomingDocuments" xml:space="preserve">
    <value>الوثائق الواردة</value>
  </data>
  <data name="OutgoingDocuments" xml:space="preserve">
    <value>الوثائق الصادرة</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>الفئات</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>التقارير</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>الإعدادات</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>المساعدة</value>
  </data>
  <data name="About" xml:space="preserve">
    <value>حول البرنامج</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>معلومات</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>تأكيد</value>
  </data>
</root>
