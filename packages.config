<?xml version="1.0" encoding="utf-8"?>
<packages>
  <!-- Entity Framework and SQLite -->
  <package id="EntityFramework" version="6.4.4" targetFramework="net48" />
  <package id="System.Data.SQLite" version="1.0.118.0" targetFramework="net48" />
  <package id="System.Data.SQLite.Core" version="1.0.118.0" targetFramework="net48" />
  <package id="System.Data.SQLite.EF6" version="1.0.118.0" targetFramework="net48" />
  <package id="System.Data.SQLite.Linq" version="1.0.118.0" targetFramework="net48" />
  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="1.0.118.0" targetFramework="net48" />
  
  <!-- Dependency Injection -->
  <package id="Microsoft.Extensions.DependencyInjection" version="3.1.32" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="3.1.32" targetFramework="net48" />
  
  <!-- Logging -->
  <package id="NLog" version="4.7.15" targetFramework="net48" />
  
  <!-- Testing -->
  <package id="MSTest.TestFramework" version="2.2.10" targetFramework="net48" />
  <package id="MSTest.TestAdapter" version="2.2.10" targetFramework="net48" />
  <package id="Moq" version="4.20.70" targetFramework="net48" />
  
  <!-- Utilities -->
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
</packages>
