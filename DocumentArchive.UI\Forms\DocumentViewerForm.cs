using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.UI.Helpers;

namespace DocumentArchive.UI.Forms
{
    public partial class DocumentViewerForm : Form
    {
        private readonly IDocumentService _documentService;
        private readonly IFileService _fileService;
        private DocumentDto _currentDocument;

        public DocumentViewerForm(IDocumentService documentService, IFileService fileService)
        {
            _documentService = documentService ?? throw new ArgumentNullException(nameof(documentService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Configure form for Arabic
            UIHelper.ConfigureFormForArabic(this);
            
            // Style buttons
            UIHelper.StyleSecondaryButton(openFileButton);
            UIHelper.StyleSecondaryButton(editButton);
            UIHelper.StyleSecondaryButton(closeButton);
            UIHelper.StyleWarningButton(deleteButton);
            
            // Set form properties
            WindowState = FormWindowState.Maximized;
            MinimumSize = new System.Drawing.Size(800, 600);
        }

        public async Task LoadDocumentAsync(int documentId)
        {
            try
            {
                var loadingForm = MessageHelper.ShowLoadingMessage("جاري تحميل الوثيقة...");
                
                try
                {
                    _currentDocument = await _documentService.GetByIdAsync(documentId);
                    
                    if (_currentDocument != null)
                    {
                        DisplayDocumentInfo();
                        await LoadFilePreviewAsync();
                    }
                    else
                    {
                        MessageHelper.ShowError("لم يتم العثور على الوثيقة المطلوبة");
                        Close();
                    }
                }
                finally
                {
                    MessageHelper.CloseLoadingMessage(loadingForm);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الوثيقة");
                Close();
            }
        }

        private void DisplayDocumentInfo()
        {
            if (_currentDocument == null) return;

            // Update form title
            Text = $"معاينة الوثيقة - {_currentDocument.Title}";

            // Display document information
            documentNumberLabel.Text = _currentDocument.DocumentNumber;
            titleLabel.Text = _currentDocument.Title;
            dateLabel.Text = _currentDocument.Date.ToString("yyyy/MM/dd");
            typeLabel.Text = _currentDocument.TypeDisplayName;
            senderRecipientLabel.Text = _currentDocument.SenderRecipient;
            categoryLabel.Text = _currentDocument.CategoryName;
            statusLabel.Text = _currentDocument.StatusDisplayName;
            descriptionTextBox.Text = _currentDocument.Description ?? "لا يوجد وصف";
            createdDateLabel.Text = _currentDocument.CreatedDate.ToString("yyyy/MM/dd HH:mm");
            modifiedDateLabel.Text = _currentDocument.ModifiedDate.ToString("yyyy/MM/dd HH:mm");

            // File information
            if (_currentDocument.HasFile)
            {
                fileInfoLabel.Text = $"الملف: {_currentDocument.OriginalFileName} ({_currentDocument.FileSizeFormatted})";
                openFileButton.Enabled = true;
                previewPanel.Visible = true;
            }
            else
            {
                fileInfoLabel.Text = "لا يوجد ملف مرفق";
                openFileButton.Enabled = false;
                previewPanel.Visible = false;
            }

            // Set status color
            statusLabel.ForeColor = UIHelper.GetStatusColor(_currentDocument.Status);
        }

        private async Task LoadFilePreviewAsync()
        {
            if (_currentDocument == null || !_currentDocument.HasFile) return;

            try
            {
                var filePath = _currentDocument.FilePath;
                
                if (!_fileService.FileExists(filePath))
                {
                    previewLabel.Text = "الملف غير موجود في المسار المحدد";
                    return;
                }

                var extension = Path.GetExtension(_currentDocument.OriginalFileName).ToLowerInvariant();
                
                switch (extension)
                {
                    case ".jpg":
                    case ".jpeg":
                    case ".png":
                        await LoadImagePreviewAsync(filePath);
                        break;
                    case ".txt":
                        await LoadTextPreviewAsync(filePath);
                        break;
                    case ".pdf":
                    case ".doc":
                    case ".docx":
                    case ".xls":
                    case ".xlsx":
                        ShowDocumentPreviewMessage(extension);
                        break;
                    default:
                        previewLabel.Text = "معاينة هذا النوع من الملفات غير مدعومة";
                        break;
                }
            }
            catch (Exception ex)
            {
                previewLabel.Text = $"فشل في تحميل معاينة الملف: {ex.Message}";
            }
        }

        private async Task LoadImagePreviewAsync(string filePath)
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var image = System.Drawing.Image.FromFile(filePath))
                    {
                        // Create a copy to avoid file locking
                        var previewImage = new System.Drawing.Bitmap(image);
                        
                        this.Invoke(new Action(() =>
                        {
                            previewPictureBox.Image?.Dispose();
                            previewPictureBox.Image = previewImage;
                            previewPictureBox.SizeMode = PictureBoxSizeMode.Zoom;
                            previewPictureBox.Visible = true;
                            previewLabel.Visible = false;
                        }));
                    }
                });
            }
            catch (Exception ex)
            {
                previewLabel.Text = $"فشل في تحميل الصورة: {ex.Message}";
            }
        }

        private async Task LoadTextPreviewAsync(string filePath)
        {
            try
            {
                var content = await Task.Run(() => File.ReadAllText(filePath, System.Text.Encoding.UTF8));
                
                // Limit preview to first 1000 characters
                if (content.Length > 1000)
                {
                    content = content.Substring(0, 1000) + "\n\n... (تم اقتطاع النص)";
                }

                previewTextBox.Text = content;
                previewTextBox.Visible = true;
                previewLabel.Visible = false;
                previewPictureBox.Visible = false;
            }
            catch (Exception ex)
            {
                previewLabel.Text = $"فشل في تحميل النص: {ex.Message}";
            }
        }

        private void ShowDocumentPreviewMessage(string extension)
        {
            var fileType = extension switch
            {
                ".pdf" => "PDF",
                ".doc" or ".docx" => "Word",
                ".xls" or ".xlsx" => "Excel",
                _ => "المستند"
            };

            previewLabel.Text = $"لمعاينة ملف {fileType}، انقر على 'فتح الملف' لفتحه في التطبيق المناسب";
            previewLabel.Visible = true;
            previewPictureBox.Visible = false;
            previewTextBox.Visible = false;
        }

        private void openFileButton_Click(object sender, EventArgs e)
        {
            if (_currentDocument?.HasFile == true)
            {
                OpenFileInDefaultApplication();
            }
        }

        private void OpenFileInDefaultApplication()
        {
            try
            {
                if (!_fileService.FileExists(_currentDocument.FilePath))
                {
                    MessageHelper.ShowError("الملف غير موجود في المسار المحدد");
                    return;
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = _currentDocument.FilePath,
                    UseShellExecute = true,
                    Verb = "open"
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في فتح الملف");
            }
        }

        private void editButton_Click(object sender, EventArgs e)
        {
            if (_currentDocument != null)
            {
                try
                {
                    var documentForm = Program.GetService<DocumentForm>();
                    documentForm.LoadDocument(_currentDocument.Id);
                    
                    documentForm.DocumentSaved += (s, args) =>
                    {
                        // Reload document after edit
                        LoadDocumentAsync(_currentDocument.Id);
                    };
                    
                    UIHelper.CenterFormOnParent(documentForm, this);
                    documentForm.ShowDialog();
                }
                catch (Exception ex)
                {
                    MessageHelper.ShowException(ex, "فشل في فتح نموذج التعديل");
                }
            }
        }

        private async void deleteButton_Click(object sender, EventArgs e)
        {
            if (_currentDocument != null)
            {
                if (MessageHelper.ShowDeleteConfirmation(_currentDocument.Title))
                {
                    try
                    {
                        var success = await _documentService.DeleteAsync(_currentDocument.Id);
                        if (success)
                        {
                            MessageHelper.ShowSuccess("تم حذف الوثيقة بنجاح");
                            Close();
                        }
                        else
                        {
                            MessageHelper.ShowError("فشل في حذف الوثيقة");
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageHelper.ShowException(ex, "فشل في حذف الوثيقة");
                    }
                }
            }
        }

        private void closeButton_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void DocumentViewerForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Clean up resources
            previewPictureBox.Image?.Dispose();
        }

        private void DocumentViewerForm_KeyDown(object sender, KeyEventArgs e)
        {
            // Keyboard shortcuts
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            }
            else if (e.Control && e.KeyCode == Keys.E)
            {
                editButton_Click(sender, e);
            }
            else if (e.Control && e.KeyCode == Keys.O)
            {
                openFileButton_Click(sender, e);
            }
        }
    }
}
