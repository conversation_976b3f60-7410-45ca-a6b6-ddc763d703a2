namespace DocumentArchive.Models.Enums
{
    /// <summary>
    /// Represents the status of a document in the workflow
    /// </summary>
    public enum DocumentStatus
    {
        /// <summary>
        /// Document is in draft state
        /// </summary>
        Draft = 1,

        /// <summary>
        /// Document has been processed
        /// </summary>
        Processed = 2,

        /// <summary>
        /// Document has been archived
        /// </summary>
        Archived = 3,

        /// <summary>
        /// Document has been deleted (soft delete)
        /// </summary>
        Deleted = 4
    }
}
