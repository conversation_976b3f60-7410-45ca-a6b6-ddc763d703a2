using System.Data.Entity;
using DocumentArchive.Models.Entities;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Data.Context
{
    /// <summary>
    /// Entity Framework DbContext for Document Archive System
    /// </summary>
    public class DocumentArchiveContext : DbContext
    {
        public DocumentArchiveContext() : base("DocumentArchiveConnection")
        {
            // Enable lazy loading
            Configuration.LazyLoadingEnabled = true;
            Configuration.ProxyCreationEnabled = true;
        }

        /// <summary>
        /// Documents DbSet
        /// </summary>
        public DbSet<Document> Documents { get; set; }

        /// <summary>
        /// Categories DbSet
        /// </summary>
        public DbSet<Category> Categories { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Document entity
            modelBuilder.Entity<Document>()
                .HasRequired(d => d.Category)
                .WithMany(c => c.Documents)
                .HasForeignKey(d => d.CategoryId)
                .WillCascadeOnDelete(false);

            // Configure indexes
            modelBuilder.Entity<Document>()
                .HasIndex(d => d.DocumentNumber)
                .IsUnique();

            modelBuilder.Entity<Document>()
                .HasIndex(d => d.Date);

            modelBuilder.Entity<Document>()
                .HasIndex(d => d.Type);

            modelBuilder.Entity<Document>()
                .HasIndex(d => d.Status);

            modelBuilder.Entity<Category>()
                .HasIndex(c => c.Name);

            // Configure enum properties
            modelBuilder.Entity<Document>()
                .Property(d => d.Type)
                .HasColumnType("INTEGER");

            modelBuilder.Entity<Document>()
                .Property(d => d.Status)
                .HasColumnType("INTEGER");
        }
    }
}
