using System.Windows.Forms;
using DocumentArchive.UI.Properties;

namespace DocumentArchive.UI.Helpers
{
    /// <summary>
    /// Helper class for displaying messages and dialogs
    /// </summary>
    public static class MessageHelper
    {
        /// <summary>
        /// Show error message
        /// </summary>
        public static void ShowError(string message, string title = null)
        {
            MessageBox.Show(
                message,
                title ?? Resources.Error,
                MessageBoxButtons.OK,
                MessageBoxIcon.Error,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RtlReading
            );
        }

        /// <summary>
        /// Show warning message
        /// </summary>
        public static void ShowWarning(string message, string title = null)
        {
            MessageBox.Show(
                message,
                title ?? Resources.Warning,
                MessageBoxButtons.OK,
                MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RtlReading
            );
        }

        /// <summary>
        /// Show information message
        /// </summary>
        public static void ShowInformation(string message, string title = null)
        {
            MessageBox.Show(
                message,
                title ?? Resources.Information,
                MessageBoxButtons.OK,
                MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RtlReading
            );
        }

        /// <summary>
        /// Show success message
        /// </summary>
        public static void ShowSuccess(string message, string title = null)
        {
            MessageBox.Show(
                message,
                title ?? Resources.Information,
                MessageBoxButtons.OK,
                MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RtlReading
            );
        }

        /// <summary>
        /// Show confirmation dialog
        /// </summary>
        public static bool ShowConfirmation(string message, string title = null)
        {
            var result = MessageBox.Show(
                message,
                title ?? Resources.Confirmation,
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2,
                MessageBoxOptions.RtlReading
            );

            return result == DialogResult.Yes;
        }

        /// <summary>
        /// Show confirmation dialog with custom buttons
        /// </summary>
        public static DialogResult ShowConfirmationWithCancel(string message, string title = null)
        {
            return MessageBox.Show(
                message,
                title ?? Resources.Confirmation,
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button3,
                MessageBoxOptions.RtlReading
            );
        }

        /// <summary>
        /// Show validation errors
        /// </summary>
        public static void ShowValidationErrors(System.Collections.Generic.List<string> errors, string title = null)
        {
            if (errors == null || errors.Count == 0) return;

            var message = "تم العثور على الأخطاء التالية:\n\n";
            for (int i = 0; i < errors.Count; i++)
            {
                message += $"• {errors[i]}";
                if (i < errors.Count - 1)
                    message += "\n";
            }

            ShowError(message, title ?? "أخطاء في التحقق من البيانات");
        }

        /// <summary>
        /// Show exception details
        /// </summary>
        public static void ShowException(System.Exception ex, string userMessage = null)
        {
            var message = userMessage ?? "حدث خطأ غير متوقع";
            message += $"\n\nتفاصيل الخطأ:\n{ex.Message}";

            if (ex.InnerException != null)
            {
                message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
            }

            ShowError(message, "خطأ في النظام");
        }

        /// <summary>
        /// Show operation result message
        /// </summary>
        public static void ShowOperationResult(bool success, string successMessage, string errorMessage)
        {
            if (success)
            {
                ShowSuccess(successMessage);
            }
            else
            {
                ShowError(errorMessage);
            }
        }

        /// <summary>
        /// Show delete confirmation
        /// </summary>
        public static bool ShowDeleteConfirmation(string itemName = null)
        {
            var message = string.IsNullOrEmpty(itemName)
                ? "هل أنت متأكد من رغبتك في حذف هذا العنصر؟"
                : $"هل أنت متأكد من رغبتك في حذف '{itemName}'؟";

            message += "\n\nلا يمكن التراجع عن هذا الإجراء.";

            return ShowConfirmation(message, "تأكيد الحذف");
        }

        /// <summary>
        /// Show save confirmation when closing with unsaved changes
        /// </summary>
        public static DialogResult ShowUnsavedChangesConfirmation()
        {
            var message = "يوجد تغييرات لم يتم حفظها.\n\nهل تريد حفظ التغييرات قبل الإغلاق؟";

            return MessageBox.Show(
                message,
                "تغييرات غير محفوظة",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.RtlReading
            );
        }

        /// <summary>
        /// Show file operation error
        /// </summary>
        public static void ShowFileError(string operation, string fileName = null)
        {
            var message = $"فشل في {operation}";
            if (!string.IsNullOrEmpty(fileName))
            {
                message += $" للملف: {fileName}";
            }

            message += "\n\nتأكد من أن الملف غير مستخدم من قبل تطبيق آخر وأن لديك الصلاحيات المطلوبة.";

            ShowError(message, "خطأ في العملية");
        }

        /// <summary>
        /// Show network/database error
        /// </summary>
        public static void ShowDatabaseError(string operation = null)
        {
            var message = "فشل في الاتصال بقاعدة البيانات";
            if (!string.IsNullOrEmpty(operation))
            {
                message += $" أثناء {operation}";
            }

            message += "\n\nتأكد من أن قاعدة البيانات متاحة وأن لديك الصلاحيات المطلوبة.";

            ShowError(message, "خطأ في قاعدة البيانات");
        }

        /// <summary>
        /// Show loading message (for long operations)
        /// </summary>
        public static Form ShowLoadingMessage(string message = null)
        {
            var loadingForm = new Form
            {
                Text = "جاري التحميل...",
                Size = new System.Drawing.Size(300, 100),
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                ControlBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            var label = new Label
            {
                Text = message ?? "جاري المعالجة، يرجى الانتظار...",
                Dock = DockStyle.Fill,
                TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                Font = new System.Drawing.Font("Tahoma", 9F)
            };

            loadingForm.Controls.Add(label);
            loadingForm.Show();

            return loadingForm;
        }

        /// <summary>
        /// Close loading message
        /// </summary>
        public static void CloseLoadingMessage(Form loadingForm)
        {
            if (loadingForm != null && !loadingForm.IsDisposed)
            {
                loadingForm.Close();
                loadingForm.Dispose();
            }
        }
    }
}
