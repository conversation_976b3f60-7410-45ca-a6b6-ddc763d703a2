using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Business.Interfaces
{
    /// <summary>
    /// Service interface for Document operations
    /// </summary>
    public interface IDocumentService
    {
        /// <summary>
        /// Get document by ID
        /// </summary>
        Task<DocumentDto> GetByIdAsync(int id);

        /// <summary>
        /// Get all documents with filtering and pagination
        /// </summary>
        Task<PagedResult<DocumentDto>> GetAllAsync(SearchCriteriaDto criteria);

        /// <summary>
        /// Create new document
        /// </summary>
        Task<DocumentDto> CreateAsync(DocumentDto documentDto, string filePath = null);

        /// <summary>
        /// Update existing document
        /// </summary>
        Task<DocumentDto> UpdateAsync(DocumentDto documentDto, string newFilePath = null);

        /// <summary>
        /// Delete document (soft delete)
        /// </summary>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Search documents by text
        /// </summary>
        Task<List<DocumentDto>> SearchAsync(string searchText);

        /// <summary>
        /// Get documents by category
        /// </summary>
        Task<List<DocumentDto>> GetByCategoryAsync(int categoryId);

        /// <summary>
        /// Get documents by status
        /// </summary>
        Task<List<DocumentDto>> GetByStatusAsync(DocumentStatus status);

        /// <summary>
        /// Get documents by date range
        /// </summary>
        Task<List<DocumentDto>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Generate next document number
        /// </summary>
        Task<string> GenerateDocumentNumberAsync(DocumentType type);

        /// <summary>
        /// Validate document data
        /// </summary>
        Task<List<string>> ValidateDocumentAsync(DocumentDto documentDto);

        /// <summary>
        /// Change document status
        /// </summary>
        Task<bool> ChangeStatusAsync(int id, DocumentStatus newStatus);

        /// <summary>
        /// Get document statistics
        /// </summary>
        Task<Dictionary<string, int>> GetStatisticsAsync();

        /// <summary>
        /// Export documents to Excel
        /// </summary>
        Task<byte[]> ExportToExcelAsync(SearchCriteriaDto criteria);
    }
}
