using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Business.Mappers;
using DocumentArchive.Business.Validators;
using DocumentArchive.Data.Interfaces;
using DocumentArchive.Models.DTOs;
using NLog;

namespace DocumentArchive.Business.Services
{
    /// <summary>
    /// Service for Category operations
    /// </summary>
    public class CategoryService : ICategoryService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly CategoryValidator _validator;

        public CategoryService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _validator = new CategoryValidator(_unitOfWork);
        }

        public async Task<CategoryDto> GetByIdAsync(int id)
        {
            try
            {
                var category = await _unitOfWork.Categories.GetByIdAsync(id);
                return CategoryMapper.ToDto(category);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error getting category by ID: {id}");
                throw;
            }
        }

        public async Task<List<CategoryDto>> GetAllAsync()
        {
            try
            {
                var categories = await _unitOfWork.Categories.GetAllAsync();
                return CategoryMapper.ToDtoList(categories);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error getting all categories");
                throw;
            }
        }

        public async Task<List<CategoryDto>> GetActiveAsync()
        {
            try
            {
                var categories = await _unitOfWork.Categories.GetActiveAsync();
                return CategoryMapper.ToDtoList(categories);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error getting active categories");
                throw;
            }
        }

        public async Task<CategoryDto> CreateAsync(CategoryDto categoryDto)
        {
            try
            {
                // Validate input
                var validationErrors = await _validator.ValidateAsync(categoryDto, false);
                if (validationErrors.Count > 0)
                {
                    throw new ArgumentException($"Validation failed: {string.Join(", ", validationErrors)}");
                }

                var category = CategoryMapper.ToEntity(categoryDto);
                category.CreatedDate = DateTime.Now;
                category.ModifiedDate = DateTime.Now;

                var createdCategory = await _unitOfWork.Categories.AddAsync(category);
                await _unitOfWork.SaveChangesAsync();

                Logger.Info($"Category created successfully: {createdCategory.Name} (ID: {createdCategory.Id})");
                return CategoryMapper.ToDto(createdCategory);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error creating category: {categoryDto?.Name}");
                throw;
            }
        }

        public async Task<CategoryDto> UpdateAsync(CategoryDto categoryDto)
        {
            try
            {
                // Validate input
                var validationErrors = await _validator.ValidateAsync(categoryDto, true);
                if (validationErrors.Count > 0)
                {
                    throw new ArgumentException($"Validation failed: {string.Join(", ", validationErrors)}");
                }

                var existingCategory = await _unitOfWork.Categories.GetByIdAsync(categoryDto.Id);
                if (existingCategory == null)
                {
                    throw new ArgumentException("Category not found");
                }

                CategoryMapper.UpdateEntityFromDto(existingCategory, categoryDto);
                existingCategory.ModifiedDate = DateTime.Now;

                var updatedCategory = await _unitOfWork.Categories.UpdateAsync(existingCategory);
                await _unitOfWork.SaveChangesAsync();

                Logger.Info($"Category updated successfully: {updatedCategory.Name} (ID: {updatedCategory.Id})");
                return CategoryMapper.ToDto(updatedCategory);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error updating category: {categoryDto?.Name} (ID: {categoryDto?.Id})");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var validationErrors = await _validator.ValidateDeleteAsync(id);
                if (validationErrors.Count > 0)
                {
                    Logger.Warn($"Category deletion validation failed: {string.Join(", ", validationErrors)}");
                    // Still proceed with soft delete if category has documents
                }

                var result = await _unitOfWork.Categories.DeleteAsync(id);
                if (result)
                {
                    await _unitOfWork.SaveChangesAsync();
                    Logger.Info($"Category deleted/deactivated successfully: ID {id}");
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error deleting category: ID {id}");
                throw;
            }
        }

        public async Task<List<CategoryDto>> SearchAsync(string searchText)
        {
            try
            {
                var categories = await _unitOfWork.Categories.SearchAsync(searchText);
                return CategoryMapper.ToDtoList(categories);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error searching categories: {searchText}");
                throw;
            }
        }

        public async Task<List<string>> ValidateCategoryAsync(CategoryDto categoryDto)
        {
            try
            {
                return await _validator.ValidateAsync(categoryDto, categoryDto.Id > 0);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error validating category: {categoryDto?.Name}");
                throw;
            }
        }

        public async Task<CategoryDto> GetWithDocumentCountAsync(int id)
        {
            try
            {
                var category = await _unitOfWork.Categories.GetWithDocumentCountAsync(id);
                return CategoryMapper.ToDto(category);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error getting category with document count: ID {id}");
                throw;
            }
        }

        public async Task<bool> CanDeleteAsync(int id)
        {
            try
            {
                var validationErrors = await _validator.ValidateDeleteAsync(id);
                return validationErrors.Count == 0;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error checking if category can be deleted: ID {id}");
                return false;
            }
        }
    }
}
