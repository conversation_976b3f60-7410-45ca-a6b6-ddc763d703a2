@echo off
echo ========================================
echo Document Archive System - Development Setup
echo ========================================

echo.
echo This script will set up the development environment for the Document Archive System.
echo.
echo Prerequisites:
echo - Visual Studio 2019 or later
echo - .NET Framework 4.8 Developer Pack
echo - NuGet CLI (optional, for command line builds)
echo.
pause

echo.
echo [1/4] Creating necessary directories...
if not exist "Data" mkdir Data
if not exist "Documents" mkdir Documents
if not exist "Logs" mkdir Logs
echo Directories created successfully.

echo.
echo [2/4] Checking .NET Framework 4.8...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release | find "528040" >nul
if %errorlevel% equ 0 (
    echo .NET Framework 4.8 is installed.
) else (
    echo WARNING: .NET Framework 4.8 may not be installed.
    echo Please install .NET Framework 4.8 Developer Pack from:
    echo https://dotnet.microsoft.com/download/dotnet-framework/net48
)

echo.
echo [3/4] Checking for Visual Studio...
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019" (
    echo Visual Studio 2019 found.
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2022" (
    echo Visual Studio 2022 found.
) else (
    echo WARNING: Visual Studio not found in standard locations.
    echo Please ensure Visual Studio 2019 or later is installed.
)

echo.
echo [4/4] Checking for NuGet...
nuget >nul 2>&1
if %errorlevel% equ 0 (
    echo NuGet CLI is available.
) else (
    echo INFO: NuGet CLI not found. You can still build using Visual Studio.
    echo To install NuGet CLI, download from: https://www.nuget.org/downloads
)

echo.
echo ========================================
echo Development Environment Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Open DocumentArchive.sln in Visual Studio
echo 2. Build the solution (Ctrl+Shift+B)
echo 3. Set DocumentArchive.UI as startup project
echo 4. Run the application (F5)
echo.
echo Alternative: Run build.bat for command-line build
echo.
pause
