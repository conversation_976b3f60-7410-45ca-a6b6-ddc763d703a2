using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;
using DocumentArchive.Business.Helpers;
using DocumentArchive.Business.Interfaces;
using NLog;

namespace DocumentArchive.Business.Services
{
    /// <summary>
    /// Service for file operations
    /// </summary>
    public class FileService : IFileService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly string _baseStoragePath;

        public FileService()
        {
            _baseStoragePath = ConfigurationManager.AppSettings["DocumentStoragePath"] ?? 
                              Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents");
            
            FileHelper.EnsureDirectoryExists(_baseStoragePath);
        }

        public async Task<string> SaveFileAsync(string sourceFilePath, string originalFileName, int documentId, int year, string documentType)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    throw new FileNotFoundException("Source file not found", sourceFilePath);

                var storageDirectory = GetStorageDirectory(year, documentType);
                FileHelper.EnsureDirectoryExists(storageDirectory);

                var safeFileName = FileHelper.GenerateSafeFileName(originalFileName);
                var fileName = $"{documentId}_{safeFileName}";
                var uniqueFileName = FileHelper.GenerateUniqueFileName(storageDirectory, fileName);
                var destinationPath = Path.Combine(storageDirectory, uniqueFileName);

                await Task.Run(() => File.Copy(sourceFilePath, destinationPath, true));

                Logger.Info($"File saved successfully: {destinationPath}");
                return destinationPath;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error saving file: {sourceFilePath}");
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return false;

                await Task.Run(() => File.Delete(filePath));
                Logger.Info($"File deleted successfully: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error deleting file: {filePath}");
                return false;
            }
        }

        public async Task<string> CopyFileAsync(string sourceFilePath, string destinationPath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    throw new FileNotFoundException("Source file not found", sourceFilePath);

                var destinationDirectory = Path.GetDirectoryName(destinationPath);
                FileHelper.EnsureDirectoryExists(destinationDirectory);

                await Task.Run(() => File.Copy(sourceFilePath, destinationPath, true));
                return destinationPath;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error copying file from {sourceFilePath} to {destinationPath}");
                throw;
            }
        }

        public bool FileExists(string filePath)
        {
            return !string.IsNullOrEmpty(filePath) && File.Exists(filePath);
        }

        public long GetFileSize(string filePath)
        {
            if (!FileExists(filePath)) return 0;

            try
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error getting file size: {filePath}");
                return 0;
            }
        }

        public string GetMimeType(string fileName)
        {
            return FileHelper.GetMimeType(fileName);
        }

        public bool IsValidFileType(string fileName)
        {
            return FileHelper.IsValidFileType(fileName);
        }

        public List<string> GetSupportedExtensions()
        {
            return FileHelper.GetSupportedExtensions();
        }

        public string FormatFileSize(long bytes)
        {
            return FileHelper.FormatFileSize(bytes);
        }

        public async Task<string> GenerateThumbnailAsync(string filePath)
        {
            try
            {
                if (!FileExists(filePath) || !FileHelper.IsImageFile(filePath))
                    return null;

                var thumbnailDirectory = Path.Combine(Path.GetDirectoryName(filePath), "Thumbnails");
                FileHelper.EnsureDirectoryExists(thumbnailDirectory);

                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var thumbnailPath = Path.Combine(thumbnailDirectory, $"{fileName}_thumb.jpg");

                await Task.Run(() =>
                {
                    using (var originalImage = Image.FromFile(filePath))
                    {
                        var thumbnailSize = CalculateThumbnailSize(originalImage.Size, 150, 150);
                        using (var thumbnail = new Bitmap(thumbnailSize.Width, thumbnailSize.Height))
                        {
                            using (var graphics = Graphics.FromImage(thumbnail))
                            {
                                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                                graphics.DrawImage(originalImage, 0, 0, thumbnailSize.Width, thumbnailSize.Height);
                            }
                            thumbnail.Save(thumbnailPath, ImageFormat.Jpeg);
                        }
                    }
                });

                return thumbnailPath;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error generating thumbnail for: {filePath}");
                return null;
            }
        }

        public async Task<bool> ValidateFileIntegrityAsync(string filePath)
        {
            try
            {
                if (!FileExists(filePath)) return false;

                // Basic integrity check - try to read the file
                await Task.Run(() =>
                {
                    using (var stream = File.OpenRead(filePath))
                    {
                        var buffer = new byte[1024];
                        stream.Read(buffer, 0, buffer.Length);
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"File integrity validation failed: {filePath}");
                return false;
            }
        }

        public string GetStorageDirectory(int year, string documentType)
        {
            return Path.Combine(_baseStoragePath, year.ToString(), documentType);
        }

        public async Task<int> CleanupOrphanedFilesAsync()
        {
            try
            {
                var cleanedCount = 0;
                
                // This would require database access to check which files are referenced
                // For now, just return 0 as this would be implemented with database integration
                
                await Task.CompletedTask;
                return cleanedCount;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error during orphaned files cleanup");
                return 0;
            }
        }

        private Size CalculateThumbnailSize(Size originalSize, int maxWidth, int maxHeight)
        {
            var ratioX = (double)maxWidth / originalSize.Width;
            var ratioY = (double)maxHeight / originalSize.Height;
            var ratio = Math.Min(ratioX, ratioY);

            var newWidth = (int)(originalSize.Width * ratio);
            var newHeight = (int)(originalSize.Height * ratio);

            return new Size(newWidth, newHeight);
        }
    }
}
