using System;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Business.Helpers
{
    /// <summary>
    /// Helper class for document numbering
    /// </summary>
    public static class NumberingHelper
    {
        /// <summary>
        /// Generate document number prefix based on type
        /// </summary>
        public static string GetDocumentPrefix(DocumentType type)
        {
            return type == DocumentType.Incoming ? "IN" : "OUT";
        }

        /// <summary>
        /// Parse document number to extract components
        /// </summary>
        public static (DocumentType Type, int Year, int Number) ParseDocumentNumber(string documentNumber)
        {
            if (string.IsNullOrEmpty(documentNumber))
                throw new ArgumentException("Document number cannot be null or empty");

            var parts = documentNumber.Split('-');
            if (parts.Length != 3)
                throw new ArgumentException("Invalid document number format");

            // Parse type
            DocumentType type;
            switch (parts[0].ToUpper())
            {
                case "IN":
                    type = DocumentType.Incoming;
                    break;
                case "OUT":
                    type = DocumentType.Outgoing;
                    break;
                default:
                    throw new ArgumentException("Invalid document type prefix");
            }

            // Parse year
            if (!int.TryParse(parts[1], out int year))
                throw new ArgumentException("Invalid year in document number");

            // Parse number
            if (!int.TryParse(parts[2], out int number))
                throw new ArgumentException("Invalid number in document number");

            return (type, year, number);
        }

        /// <summary>
        /// Validate document number format
        /// </summary>
        public static bool IsValidDocumentNumber(string documentNumber)
        {
            try
            {
                ParseDocumentNumber(documentNumber);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Generate next document number
        /// </summary>
        public static string GenerateDocumentNumber(DocumentType type, int year, int nextNumber)
        {
            var prefix = GetDocumentPrefix(type);
            return $"{prefix}-{year}-{nextNumber:D4}";
        }

        /// <summary>
        /// Get current year
        /// </summary>
        public static int GetCurrentYear()
        {
            return DateTime.Now.Year;
        }

        /// <summary>
        /// Get document type display name in Arabic
        /// </summary>
        public static string GetDocumentTypeDisplayName(DocumentType type)
        {
            return type == DocumentType.Incoming ? "وارد" : "صادر";
        }

        /// <summary>
        /// Get document status display name in Arabic
        /// </summary>
        public static string GetDocumentStatusDisplayName(DocumentStatus status)
        {
            switch (status)
            {
                case DocumentStatus.Draft:
                    return "مسودة";
                case DocumentStatus.Processed:
                    return "معالج";
                case DocumentStatus.Archived:
                    return "مؤرشف";
                case DocumentStatus.Deleted:
                    return "محذوف";
                default:
                    return "غير محدد";
            }
        }

        /// <summary>
        /// Get next status in workflow
        /// </summary>
        public static DocumentStatus? GetNextStatus(DocumentStatus currentStatus)
        {
            switch (currentStatus)
            {
                case DocumentStatus.Draft:
                    return DocumentStatus.Processed;
                case DocumentStatus.Processed:
                    return DocumentStatus.Archived;
                case DocumentStatus.Archived:
                    return null; // No next status
                case DocumentStatus.Deleted:
                    return null; // Cannot change from deleted
                default:
                    return null;
            }
        }

        /// <summary>
        /// Get previous status in workflow
        /// </summary>
        public static DocumentStatus? GetPreviousStatus(DocumentStatus currentStatus)
        {
            switch (currentStatus)
            {
                case DocumentStatus.Draft:
                    return null; // No previous status
                case DocumentStatus.Processed:
                    return DocumentStatus.Draft;
                case DocumentStatus.Archived:
                    return DocumentStatus.Processed;
                case DocumentStatus.Deleted:
                    return null; // Cannot change from deleted
                default:
                    return null;
            }
        }

        /// <summary>
        /// Check if status transition is allowed
        /// </summary>
        public static bool IsStatusTransitionAllowed(DocumentStatus fromStatus, DocumentStatus toStatus)
        {
            if (fromStatus == DocumentStatus.Deleted)
                return false; // Cannot change from deleted

            if (toStatus == DocumentStatus.Deleted)
                return true; // Can always delete (soft delete)

            switch (fromStatus)
            {
                case DocumentStatus.Draft:
                    return toStatus == DocumentStatus.Processed;
                case DocumentStatus.Processed:
                    return toStatus == DocumentStatus.Archived || toStatus == DocumentStatus.Draft;
                case DocumentStatus.Archived:
                    return toStatus == DocumentStatus.Processed;
                default:
                    return false;
            }
        }
    }
}
