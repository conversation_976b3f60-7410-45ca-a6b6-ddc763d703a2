using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DocumentArchive.Models.Entities;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Data.Interfaces
{
    /// <summary>
    /// Repository interface for Document operations
    /// </summary>
    public interface IDocumentRepository
    {
        /// <summary>
        /// Get document by ID
        /// </summary>
        Task<Document> GetByIdAsync(int id);

        /// <summary>
        /// Get all documents with optional filtering
        /// </summary>
        Task<PagedResult<Document>> GetAllAsync(SearchCriteriaDto criteria);

        /// <summary>
        /// Add new document
        /// </summary>
        Task<Document> AddAsync(Document document);

        /// <summary>
        /// Update existing document
        /// </summary>
        Task<Document> UpdateAsync(Document document);

        /// <summary>
        /// Delete document (soft delete)
        /// </summary>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Get next document number for the specified type and year
        /// </summary>
        Task<string> GetNextDocumentNumberAsync(DocumentType type, int year);

        /// <summary>
        /// Check if document number exists
        /// </summary>
        Task<bool> DocumentNumberExistsAsync(string documentNumber);

        /// <summary>
        /// Search documents by text
        /// </summary>
        Task<List<Document>> SearchAsync(string searchText);

        /// <summary>
        /// Get documents by category
        /// </summary>
        Task<List<Document>> GetByCategoryAsync(int categoryId);

        /// <summary>
        /// Get documents by status
        /// </summary>
        Task<List<Document>> GetByStatusAsync(DocumentStatus status);

        /// <summary>
        /// Get documents by date range
        /// </summary>
        Task<List<Document>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Get document count by type
        /// </summary>
        Task<int> GetCountByTypeAsync(DocumentType type);

        /// <summary>
        /// Get document count by status
        /// </summary>
        Task<int> GetCountByStatusAsync(DocumentStatus status);
    }
}
