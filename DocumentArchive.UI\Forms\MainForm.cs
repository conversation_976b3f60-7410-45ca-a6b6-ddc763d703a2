using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using DocumentArchive.UI.Extensions;
using DocumentArchive.UI.Helpers;
using DocumentArchive.UI.Properties;

namespace DocumentArchive.UI.Forms
{
    public partial class MainForm : Form
    {
        private readonly IDocumentService _documentService;
        private readonly ICategoryService _categoryService;
        private SearchCriteriaDto _currentSearchCriteria;
        private PagedResult<DocumentDto> _currentDocuments;

        public MainForm(IDocumentService documentService, ICategoryService categoryService)
        {
            _documentService = documentService ?? throw new ArgumentNullException(nameof(documentService));
            _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
            
            InitializeComponent();
            InitializeForm();
            InitializeSearchCriteria();
        }

        private void InitializeForm()
        {
            // Configure form for Arabic
            UIHelper.ConfigureFormForArabic(this);
            
            // Set form properties
            Text = Resources.ApplicationTitle;
            WindowState = Settings.Default.WindowState;
            Size = Settings.Default.WindowSize;
            
            if (Settings.Default.WindowLocation.X >= 0 && Settings.Default.WindowLocation.Y >= 0)
            {
                Location = Settings.Default.WindowLocation;
            }
            else
            {
                UIHelper.CenterFormOnScreen(this);
            }

            // Style the DataGridView
            UIHelper.StyleDataGridView(documentsDataGridView);
            
            // Configure status strip
            UpdateStatusStrip();
        }

        private void InitializeSearchCriteria()
        {
            _currentSearchCriteria = new SearchCriteriaDto
            {
                PageNumber = 1,
                PageSize = Settings.Default.PageSize,
                SortColumn = "Date",
                SortDescending = true
            };
        }

        private async void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadInitialDataAsync();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل البيانات الأولية");
            }
        }

        private async Task LoadInitialDataAsync()
        {
            var loadingForm = MessageHelper.ShowLoadingMessage("جاري تحميل البيانات...");
            
            try
            {
                // Load categories for filter
                await LoadCategoriesAsync();
                
                // Load documents
                await LoadDocumentsAsync();
                
                // Update statistics
                await UpdateStatisticsAsync();
            }
            finally
            {
                MessageHelper.CloseLoadingMessage(loadingForm);
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                var categories = await _categoryService.GetActiveAsync();
                
                // Update category filter combo box
                categoryFilterComboBox.Items.Clear();
                categoryFilterComboBox.Items.Add(new { Id = 0, Name = "جميع الفئات" });
                
                foreach (var category in categories)
                {
                    categoryFilterComboBox.Items.Add(new { Id = category.Id, Name = category.Name });
                }
                
                categoryFilterComboBox.DisplayMember = "Name";
                categoryFilterComboBox.ValueMember = "Id";
                categoryFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الفئات");
            }
        }

        private async Task LoadDocumentsAsync()
        {
            try
            {
                _currentDocuments = await _documentService.GetAllAsync(_currentSearchCriteria);
                
                // Update DataGridView
                documentsDataGridView.DataSource = _currentDocuments.Items;
                ConfigureDataGridViewColumns();
                
                // Update pagination
                UpdatePaginationControls();
                
                // Update status
                UpdateStatusStrip();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الوثائق");
            }
        }

        private void ConfigureDataGridViewColumns()
        {
            if (documentsDataGridView.Columns.Count == 0) return;

            // Hide unnecessary columns
            if (documentsDataGridView.Columns["Id"] != null)
                documentsDataGridView.Columns["Id"].Visible = false;
            
            if (documentsDataGridView.Columns["CategoryId"] != null)
                documentsDataGridView.Columns["CategoryId"].Visible = false;
            
            if (documentsDataGridView.Columns["FilePath"] != null)
                documentsDataGridView.Columns["FilePath"].Visible = false;

            // Configure visible columns
            var columnConfig = new Dictionary<string, (string Header, int Width)>
            {
                { "DocumentNumber", ("رقم الوثيقة", 120) },
                { "Title", ("العنوان", 200) },
                { "Date", ("التاريخ", 100) },
                { "TypeDisplayName", ("النوع", 80) },
                { "SenderRecipient", ("المرسل/المستقبل", 150) },
                { "CategoryName", ("الفئة", 120) },
                { "StatusDisplayName", ("الحالة", 80) },
                { "CreatedDate", ("تاريخ الإنشاء", 120) }
            };

            foreach (var config in columnConfig)
            {
                if (documentsDataGridView.Columns[config.Key] != null)
                {
                    var column = documentsDataGridView.Columns[config.Key];
                    column.HeaderText = config.Value.Header;
                    column.Width = config.Value.Width;
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                }
            }

            // Format date columns
            if (documentsDataGridView.Columns["Date"] != null)
            {
                documentsDataGridView.Columns["Date"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
            
            if (documentsDataGridView.Columns["CreatedDate"] != null)
            {
                documentsDataGridView.Columns["CreatedDate"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm";
            }
        }

        private void UpdatePaginationControls()
        {
            if (_currentDocuments == null) return;

            // Update page info
            pageInfoLabel.Text = $"الصفحة {_currentDocuments.PageNumber} من {_currentDocuments.TotalPages}";
            
            // Update navigation buttons
            previousPageButton.Enabled = _currentDocuments.HasPreviousPage;
            nextPageButton.Enabled = _currentDocuments.HasNextPage;
            
            // Update page size combo
            if (pageSizeComboBox.SelectedItem?.ToString() != _currentDocuments.PageSize.ToString())
            {
                pageSizeComboBox.SelectedItem = _currentDocuments.PageSize.ToString();
            }
        }

        private void UpdateStatusStrip()
        {
            var totalCount = _currentDocuments?.TotalCount ?? 0;
            var selectedCount = documentsDataGridView.SelectedRows.Count;
            
            totalCountLabel.Text = $"إجمالي الوثائق: {totalCount}";
            selectedCountLabel.Text = selectedCount > 0 ? $"المحدد: {selectedCount}" : "";
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var statistics = await _documentService.GetStatisticsAsync();
                
                // Update statistics in status strip or dashboard
                var statsText = $"الواردة: {statistics.GetValueOrDefault("IncomingDocuments", 0)} | " +
                               $"الصادرة: {statistics.GetValueOrDefault("OutgoingDocuments", 0)} | " +
                               $"المسودات: {statistics.GetValueOrDefault("DraftDocuments", 0)}";
                
                statisticsLabel.Text = statsText;
            }
            catch (Exception ex)
            {
                // Log error but don't show to user as this is not critical
                System.Diagnostics.Debug.WriteLine($"Failed to update statistics: {ex.Message}");
            }
        }

        private async void searchButton_Click(object sender, EventArgs e)
        {
            await PerformSearchAsync();
        }

        private async void refreshButton_Click(object sender, EventArgs e)
        {
            await LoadDocumentsAsync();
        }

        private async Task PerformSearchAsync()
        {
            try
            {
                // Update search criteria from UI
                _currentSearchCriteria.SearchText = searchTextBox.Text.Trim();
                _currentSearchCriteria.DateFrom = dateFromPicker.Checked ? (DateTime?)dateFromPicker.Value.Date : null;
                _currentSearchCriteria.DateTo = dateToPicker.Checked ? (DateTime?)dateToPicker.Value.Date : null;
                
                // Document type filter
                if (incomingRadioButton.Checked)
                    _currentSearchCriteria.DocumentType = DocumentType.Incoming;
                else if (outgoingRadioButton.Checked)
                    _currentSearchCriteria.DocumentType = DocumentType.Outgoing;
                else
                    _currentSearchCriteria.DocumentType = null;

                // Category filter
                var selectedCategory = categoryFilterComboBox.SelectedItem as dynamic;
                if (selectedCategory != null && selectedCategory.Id > 0)
                {
                    _currentSearchCriteria.CategoryIds = new List<int> { selectedCategory.Id };
                }
                else
                {
                    _currentSearchCriteria.CategoryIds.Clear();
                }

                // Status filter
                if (statusFilterComboBox.SelectedIndex > 0)
                {
                    _currentSearchCriteria.Status = (DocumentStatus)(statusFilterComboBox.SelectedIndex);
                }
                else
                {
                    _currentSearchCriteria.Status = null;
                }

                // Reset to first page
                _currentSearchCriteria.PageNumber = 1;

                await LoadDocumentsAsync();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تنفيذ البحث");
            }
        }

        private void clearFiltersButton_Click(object sender, EventArgs e)
        {
            // Clear all filters
            searchTextBox.Clear();
            dateFromPicker.Checked = false;
            dateToPicker.Checked = false;
            allDocumentsRadioButton.Checked = true;
            categoryFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndex = 0;
            
            // Reset search criteria
            InitializeSearchCriteria();
        }

        private async void previousPageButton_Click(object sender, EventArgs e)
        {
            if (_currentSearchCriteria.PageNumber > 1)
            {
                _currentSearchCriteria.PageNumber--;
                await LoadDocumentsAsync();
            }
        }

        private async void nextPageButton_Click(object sender, EventArgs e)
        {
            if (_currentDocuments != null && _currentDocuments.HasNextPage)
            {
                _currentSearchCriteria.PageNumber++;
                await LoadDocumentsAsync();
            }
        }

        private async void pageSizeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(pageSizeComboBox.SelectedItem?.ToString(), out int pageSize))
            {
                _currentSearchCriteria.PageSize = pageSize;
                _currentSearchCriteria.PageNumber = 1; // Reset to first page
                await LoadDocumentsAsync();
            }
        }

        private void documentsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            UpdateStatusStrip();
            
            // Enable/disable action buttons based on selection
            var hasSelection = documentsDataGridView.SelectedRows.Count > 0;
            editButton.Enabled = documentsDataGridView.SelectedRows.Count == 1;
            deleteButton.Enabled = hasSelection;
            viewButton.Enabled = documentsDataGridView.SelectedRows.Count == 1;
        }

        private void addButton_Click(object sender, EventArgs e)
        {
            ShowDocumentForm();
        }

        private void editButton_Click(object sender, EventArgs e)
        {
            if (documentsDataGridView.SelectedRows.Count == 1)
            {
                var selectedDocument = (DocumentDto)documentsDataGridView.SelectedRows[0].DataBoundItem;
                ShowDocumentForm(selectedDocument.Id);
            }
        }

        private async void deleteButton_Click(object sender, EventArgs e)
        {
            if (documentsDataGridView.SelectedRows.Count == 0) return;

            var selectedDocuments = documentsDataGridView.SelectedRows
                .Cast<DataGridViewRow>()
                .Select(row => (DocumentDto)row.DataBoundItem)
                .ToList();

            var message = selectedDocuments.Count == 1
                ? $"هل أنت متأكد من رغبتك في حذف الوثيقة '{selectedDocuments[0].Title}'؟"
                : $"هل أنت متأكد من رغبتك في حذف {selectedDocuments.Count} وثيقة؟";

            if (MessageHelper.ShowDeleteConfirmation(message))
            {
                await DeleteDocumentsAsync(selectedDocuments);
            }
        }

        private async Task DeleteDocumentsAsync(List<DocumentDto> documents)
        {
            var loadingForm = MessageHelper.ShowLoadingMessage("جاري حذف الوثائق...");
            
            try
            {
                var successCount = 0;
                var errorCount = 0;

                foreach (var document in documents)
                {
                    try
                    {
                        var success = await _documentService.DeleteAsync(document.Id);
                        if (success)
                            successCount++;
                        else
                            errorCount++;
                    }
                    catch
                    {
                        errorCount++;
                    }
                }

                if (successCount > 0)
                {
                    await LoadDocumentsAsync();
                    MessageHelper.ShowSuccess($"تم حذف {successCount} وثيقة بنجاح");
                }

                if (errorCount > 0)
                {
                    MessageHelper.ShowWarning($"فشل في حذف {errorCount} وثيقة");
                }
            }
            finally
            {
                MessageHelper.CloseLoadingMessage(loadingForm);
            }
        }

        private void ShowDocumentForm(int? documentId = null)
        {
            try
            {
                var documentForm = Program.GetService<DocumentForm>();
                
                if (documentId.HasValue)
                {
                    documentForm.LoadDocument(documentId.Value);
                }

                documentForm.DocumentSaved += async (s, e) => await LoadDocumentsAsync();
                
                UIHelper.CenterFormOnParent(documentForm, this);
                documentForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في فتح نموذج الوثيقة");
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Save window state
            if (WindowState == FormWindowState.Normal)
            {
                Settings.Default.WindowSize = Size;
                Settings.Default.WindowLocation = Location;
            }
            Settings.Default.WindowState = WindowState;
            Settings.Default.Save();
        }

        private void exitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void aboutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            MessageHelper.ShowInformation(
                $"{Resources.ApplicationTitle}\n\nإصدار 1.0.0\n\nنظام شامل لإدارة وأرشفة الوثائق الإلكترونية",
                Resources.About
            );
        }
    }
}
