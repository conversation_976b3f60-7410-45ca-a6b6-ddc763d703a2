using System;
using System.Threading.Tasks;

namespace DocumentArchive.Data.Interfaces
{
    /// <summary>
    /// Unit of Work pattern interface
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// Document repository
        /// </summary>
        IDocumentRepository Documents { get; }

        /// <summary>
        /// Category repository
        /// </summary>
        ICategoryRepository Categories { get; }

        /// <summary>
        /// Save all changes to the database
        /// </summary>
        Task<int> SaveChangesAsync();

        /// <summary>
        /// Begin database transaction
        /// </summary>
        void BeginTransaction();

        /// <summary>
        /// Commit database transaction
        /// </summary>
        void CommitTransaction();

        /// <summary>
        /// Rollback database transaction
        /// </summary>
        void RollbackTransaction();
    }
}
