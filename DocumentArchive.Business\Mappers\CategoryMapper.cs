using System.Collections.Generic;
using System.Linq;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Entities;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Business.Mappers
{
    /// <summary>
    /// Mapper for Category entity and DTO conversions
    /// </summary>
    public static class CategoryMapper
    {
        /// <summary>
        /// Convert Category entity to CategoryDto
        /// </summary>
        public static CategoryDto ToDto(Category category)
        {
            if (category == null) return null;

            return new CategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                IsActive = category.IsActive,
                CreatedDate = category.CreatedDate,
                ModifiedDate = category.ModifiedDate,
                DocumentCount = category.Documents?.Count(d => d.Status != DocumentStatus.Deleted) ?? 0
            };
        }

        /// <summary>
        /// Convert CategoryDto to Category entity
        /// </summary>
        public static Category ToEntity(CategoryDto categoryDto)
        {
            if (categoryDto == null) return null;

            return new Category
            {
                Id = categoryDto.Id,
                Name = categoryDto.Name,
                Description = categoryDto.Description,
                IsActive = categoryDto.IsActive,
                CreatedDate = categoryDto.CreatedDate,
                ModifiedDate = categoryDto.ModifiedDate
            };
        }

        /// <summary>
        /// Convert list of Category entities to list of CategoryDtos
        /// </summary>
        public static List<CategoryDto> ToDtoList(List<Category> categories)
        {
            if (categories == null) return new List<CategoryDto>();

            return categories.Select(ToDto).ToList();
        }

        /// <summary>
        /// Convert list of CategoryDtos to list of Category entities
        /// </summary>
        public static List<Category> ToEntityList(List<CategoryDto> categoryDtos)
        {
            if (categoryDtos == null) return new List<Category>();

            return categoryDtos.Select(ToEntity).ToList();
        }

        /// <summary>
        /// Update Category entity from CategoryDto
        /// </summary>
        public static void UpdateEntityFromDto(Category entity, CategoryDto dto)
        {
            if (entity == null || dto == null) return;

            entity.Name = dto.Name;
            entity.Description = dto.Description;
            entity.IsActive = dto.IsActive;
        }
    }
}
