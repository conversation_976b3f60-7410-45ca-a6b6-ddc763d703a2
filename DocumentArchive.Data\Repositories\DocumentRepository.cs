using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using DocumentArchive.Data.Context;
using DocumentArchive.Data.Interfaces;
using DocumentArchive.Models.Entities;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Data.Repositories
{
    /// <summary>
    /// Repository implementation for Document operations
    /// </summary>
    public class DocumentRepository : IDocumentRepository
    {
        private readonly DocumentArchiveContext _context;

        public DocumentRepository(DocumentArchiveContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Document> GetByIdAsync(int id)
        {
            return await _context.Documents
                .Include(d => d.Category)
                .FirstOrDefaultAsync(d => d.Id == id && d.Status != DocumentStatus.Deleted);
        }

        public async Task<PagedResult<Document>> GetAllAsync(SearchCriteriaDto criteria)
        {
            var query = _context.Documents
                .Include(d => d.Category)
                .Where(d => d.Status != DocumentStatus.Deleted);

            // Apply filters
            if (!string.IsNullOrEmpty(criteria.SearchText))
            {
                var searchLower = criteria.SearchText.ToLower();
                query = query.Where(d => d.Title.ToLower().Contains(searchLower) ||
                                        d.Description.ToLower().Contains(searchLower) ||
                                        d.SenderRecipient.ToLower().Contains(searchLower));
            }

            if (criteria.DateFrom.HasValue)
            {
                query = query.Where(d => d.Date >= criteria.DateFrom.Value);
            }

            if (criteria.DateTo.HasValue)
            {
                query = query.Where(d => d.Date <= criteria.DateTo.Value);
            }

            if (criteria.DocumentType.HasValue)
            {
                query = query.Where(d => d.Type == criteria.DocumentType.Value);
            }

            if (criteria.CategoryIds != null && criteria.CategoryIds.Any())
            {
                query = query.Where(d => criteria.CategoryIds.Contains(d.CategoryId));
            }

            if (criteria.Status.HasValue)
            {
                query = query.Where(d => d.Status == criteria.Status.Value);
            }

            // Apply sorting
            switch (criteria.SortColumn?.ToLower())
            {
                case "title":
                    query = criteria.SortDescending ? query.OrderByDescending(d => d.Title) : query.OrderBy(d => d.Title);
                    break;
                case "documentnumber":
                    query = criteria.SortDescending ? query.OrderByDescending(d => d.DocumentNumber) : query.OrderBy(d => d.DocumentNumber);
                    break;
                case "senderrecipient":
                    query = criteria.SortDescending ? query.OrderByDescending(d => d.SenderRecipient) : query.OrderBy(d => d.SenderRecipient);
                    break;
                case "category":
                    query = criteria.SortDescending ? query.OrderByDescending(d => d.Category.Name) : query.OrderBy(d => d.Category.Name);
                    break;
                case "status":
                    query = criteria.SortDescending ? query.OrderByDescending(d => d.Status) : query.OrderBy(d => d.Status);
                    break;
                default: // Date
                    query = criteria.SortDescending ? query.OrderByDescending(d => d.Date) : query.OrderBy(d => d.Date);
                    break;
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                .Take(criteria.PageSize)
                .ToListAsync();

            return new PagedResult<Document>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = criteria.PageNumber,
                PageSize = criteria.PageSize
            };
        }

        public async Task<Document> AddAsync(Document document)
        {
            if (document == null) throw new ArgumentNullException(nameof(document));

            document.CreatedDate = DateTime.Now;
            document.ModifiedDate = DateTime.Now;

            _context.Documents.Add(document);
            await _context.SaveChangesAsync();
            return document;
        }

        public async Task<Document> UpdateAsync(Document document)
        {
            if (document == null) throw new ArgumentNullException(nameof(document));

            var existingDocument = await _context.Documents.FindAsync(document.Id);
            if (existingDocument == null) return null;

            // Update properties
            existingDocument.Title = document.Title;
            existingDocument.Date = document.Date;
            existingDocument.SenderRecipient = document.SenderRecipient;
            existingDocument.CategoryId = document.CategoryId;
            existingDocument.Description = document.Description;
            existingDocument.Status = document.Status;
            existingDocument.FilePath = document.FilePath;
            existingDocument.OriginalFileName = document.OriginalFileName;
            existingDocument.FileSize = document.FileSize;
            existingDocument.MimeType = document.MimeType;
            existingDocument.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();
            return existingDocument;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var document = await _context.Documents.FindAsync(id);
            if (document == null) return false;

            // Soft delete
            document.Status = DocumentStatus.Deleted;
            document.ModifiedDate = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string> GetNextDocumentNumberAsync(DocumentType type, int year)
        {
            var prefix = type == DocumentType.Incoming ? "IN" : "OUT";
            var pattern = $"{prefix}-{year}-";

            var lastNumber = await _context.Documents
                .Where(d => d.DocumentNumber.StartsWith(pattern))
                .OrderByDescending(d => d.DocumentNumber)
                .Select(d => d.DocumentNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(lastNumber))
            {
                var numberPart = lastNumber.Substring(pattern.Length);
                if (int.TryParse(numberPart, out int currentNumber))
                {
                    nextNumber = currentNumber + 1;
                }
            }

            return $"{prefix}-{year}-{nextNumber:D4}";
        }

        public async Task<bool> DocumentNumberExistsAsync(string documentNumber)
        {
            return await _context.Documents
                .AnyAsync(d => d.DocumentNumber == documentNumber);
        }

        public async Task<List<Document>> SearchAsync(string searchText)
        {
            if (string.IsNullOrEmpty(searchText)) return new List<Document>();

            var searchLower = searchText.ToLower();
            return await _context.Documents
                .Include(d => d.Category)
                .Where(d => d.Status != DocumentStatus.Deleted &&
                           (d.Title.ToLower().Contains(searchLower) ||
                            d.Description.ToLower().Contains(searchLower) ||
                            d.SenderRecipient.ToLower().Contains(searchLower)))
                .OrderByDescending(d => d.Date)
                .ToListAsync();
        }

        public async Task<List<Document>> GetByCategoryAsync(int categoryId)
        {
            return await _context.Documents
                .Include(d => d.Category)
                .Where(d => d.CategoryId == categoryId && d.Status != DocumentStatus.Deleted)
                .OrderByDescending(d => d.Date)
                .ToListAsync();
        }

        public async Task<List<Document>> GetByStatusAsync(DocumentStatus status)
        {
            return await _context.Documents
                .Include(d => d.Category)
                .Where(d => d.Status == status)
                .OrderByDescending(d => d.Date)
                .ToListAsync();
        }

        public async Task<List<Document>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _context.Documents
                .Include(d => d.Category)
                .Where(d => d.Date >= fromDate && d.Date <= toDate && d.Status != DocumentStatus.Deleted)
                .OrderByDescending(d => d.Date)
                .ToListAsync();
        }

        public async Task<int> GetCountByTypeAsync(DocumentType type)
        {
            return await _context.Documents
                .CountAsync(d => d.Type == type && d.Status != DocumentStatus.Deleted);
        }

        public async Task<int> GetCountByStatusAsync(DocumentStatus status)
        {
            return await _context.Documents
                .CountAsync(d => d.Status == status);
        }
    }
}
