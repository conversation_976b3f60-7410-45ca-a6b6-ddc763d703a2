using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using DocumentArchive.UI.Extensions;
using DocumentArchive.UI.Helpers;

namespace DocumentArchive.UI.Forms
{
    public partial class DocumentForm : Form
    {
        private readonly IDocumentService _documentService;
        private readonly ICategoryService _categoryService;
        private readonly IFileService _fileService;
        
        private DocumentDto _currentDocument;
        private string _selectedFilePath;
        private bool _isEditMode;
        private bool _hasUnsavedChanges;

        public event EventHandler DocumentSaved;

        public DocumentForm(IDocumentService documentService, ICategoryService categoryService, IFileService fileService)
        {
            _documentService = documentService ?? throw new ArgumentNullException(nameof(documentService));
            _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Configure form for Arabic
            UIHelper.ConfigureFormForArabic(this);
            
            // Style buttons
            UIHelper.StylePrimaryButton(saveButton);
            UIHelper.StyleSecondaryButton(cancelButton);
            UIHelper.StyleSecondaryButton(browseFileButton);
            UIHelper.StyleWarningButton(removeFileButton);
            
            // Style text boxes and combo boxes
            UIHelper.StyleTextBox(titleTextBox);
            UIHelper.StyleTextBox(senderRecipientTextBox);
            UIHelper.StyleTextBox(descriptionTextBox);
            UIHelper.StyleComboBox(typeComboBox);
            UIHelper.StyleComboBox(categoryComboBox);
            UIHelper.StyleComboBox(statusComboBox);
            
            // Set placeholders
            titleTextBox.SetPlaceholder("أدخل عنوان الوثيقة...");
            senderRecipientTextBox.SetPlaceholder("أدخل اسم المرسل أو المستقبل...");
            descriptionTextBox.SetPlaceholder("أدخل وصف الوثيقة (اختياري)...");
            
            // Initialize combo boxes
            InitializeComboBoxes();
            
            // Set default values
            SetDefaultValues();
            
            // Wire up change events
            WireUpChangeEvents();
        }

        private void InitializeComboBoxes()
        {
            // Document type combo box
            typeComboBox.Items.Clear();
            typeComboBox.Items.Add(new { Value = DocumentType.Incoming, Text = "وارد" });
            typeComboBox.Items.Add(new { Value = DocumentType.Outgoing, Text = "صادر" });
            typeComboBox.DisplayMember = "Text";
            typeComboBox.ValueMember = "Value";
            
            // Status combo box
            statusComboBox.Items.Clear();
            statusComboBox.Items.Add(new { Value = DocumentStatus.Draft, Text = "مسودة" });
            statusComboBox.Items.Add(new { Value = DocumentStatus.Processed, Text = "معالج" });
            statusComboBox.Items.Add(new { Value = DocumentStatus.Archived, Text = "مؤرشف" });
            statusComboBox.DisplayMember = "Text";
            statusComboBox.ValueMember = "Value";
        }

        private async void DocumentForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadCategoriesAsync();
                
                if (_isEditMode && _currentDocument != null)
                {
                    LoadDocumentData();
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل بيانات النموذج");
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                var categories = await _categoryService.GetActiveAsync();
                
                categoryComboBox.Items.Clear();
                foreach (var category in categories)
                {
                    categoryComboBox.Items.Add(new { Value = category.Id, Text = category.Name });
                }
                categoryComboBox.DisplayMember = "Text";
                categoryComboBox.ValueMember = "Value";
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الفئات");
            }
        }

        private void SetDefaultValues()
        {
            if (!_isEditMode)
            {
                datePicker.Value = DateTime.Now;
                typeComboBox.SelectedIndex = 0; // Default to Incoming
                statusComboBox.SelectedIndex = 0; // Default to Draft
                
                if (categoryComboBox.Items.Count > 0)
                {
                    categoryComboBox.SelectedIndex = 0;
                }
            }
        }

        private void WireUpChangeEvents()
        {
            titleTextBox.TextChanged += OnFormDataChanged;
            senderRecipientTextBox.TextChanged += OnFormDataChanged;
            descriptionTextBox.TextChanged += OnFormDataChanged;
            datePicker.ValueChanged += OnFormDataChanged;
            typeComboBox.SelectedIndexChanged += OnFormDataChanged;
            categoryComboBox.SelectedIndexChanged += OnFormDataChanged;
            statusComboBox.SelectedIndexChanged += OnFormDataChanged;
        }

        private void OnFormDataChanged(object sender, EventArgs e)
        {
            _hasUnsavedChanges = true;
            UpdateFormTitle();
        }

        private void UpdateFormTitle()
        {
            var baseTitle = _isEditMode ? "تعديل الوثيقة" : "إضافة وثيقة جديدة";
            Text = _hasUnsavedChanges ? $"{baseTitle} *" : baseTitle;
        }

        public async void LoadDocument(int documentId)
        {
            try
            {
                _currentDocument = await _documentService.GetByIdAsync(documentId);
                if (_currentDocument != null)
                {
                    _isEditMode = true;
                    UpdateFormTitle();
                }
                else
                {
                    MessageHelper.ShowError("لم يتم العثور على الوثيقة المطلوبة");
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل بيانات الوثيقة");
                Close();
            }
        }

        private void LoadDocumentData()
        {
            if (_currentDocument == null) return;

            // Temporarily disable change events
            titleTextBox.TextChanged -= OnFormDataChanged;
            senderRecipientTextBox.TextChanged -= OnFormDataChanged;
            descriptionTextBox.TextChanged -= OnFormDataChanged;
            datePicker.ValueChanged -= OnFormDataChanged;
            typeComboBox.SelectedIndexChanged -= OnFormDataChanged;
            categoryComboBox.SelectedIndexChanged -= OnFormDataChanged;
            statusComboBox.SelectedIndexChanged -= OnFormDataChanged;

            try
            {
                // Load document data
                titleTextBox.Text = _currentDocument.Title;
                senderRecipientTextBox.Text = _currentDocument.SenderRecipient;
                descriptionTextBox.Text = _currentDocument.Description ?? string.Empty;
                datePicker.Value = _currentDocument.Date;
                
                // Set document number (read-only in edit mode)
                documentNumberTextBox.Text = _currentDocument.DocumentNumber;
                documentNumberTextBox.ReadOnly = true;
                
                // Select type
                for (int i = 0; i < typeComboBox.Items.Count; i++)
                {
                    var item = (dynamic)typeComboBox.Items[i];
                    if (item.Value.Equals(_currentDocument.Type))
                    {
                        typeComboBox.SelectedIndex = i;
                        break;
                    }
                }
                
                // Select category
                for (int i = 0; i < categoryComboBox.Items.Count; i++)
                {
                    var item = (dynamic)categoryComboBox.Items[i];
                    if (item.Value.Equals(_currentDocument.CategoryId))
                    {
                        categoryComboBox.SelectedIndex = i;
                        break;
                    }
                }
                
                // Select status
                for (int i = 0; i < statusComboBox.Items.Count; i++)
                {
                    var item = (dynamic)statusComboBox.Items[i];
                    if (item.Value.Equals(_currentDocument.Status))
                    {
                        statusComboBox.SelectedIndex = i;
                        break;
                    }
                }
                
                // Show file info if exists
                if (_currentDocument.HasFile)
                {
                    fileInfoLabel.Text = $"الملف: {_currentDocument.OriginalFileName} ({_currentDocument.FileSizeFormatted})";
                    removeFileButton.Visible = true;
                }
                else
                {
                    fileInfoLabel.Text = "لا يوجد ملف مرفق";
                    removeFileButton.Visible = false;
                }
                
                _hasUnsavedChanges = false;
                UpdateFormTitle();
            }
            finally
            {
                // Re-enable change events
                WireUpChangeEvents();
            }
        }

        private async void saveButton_Click(object sender, EventArgs e)
        {
            await SaveDocumentAsync();
        }

        private async Task SaveDocumentAsync()
        {
            try
            {
                // Validate form
                var validationErrors = ValidateForm();
                if (validationErrors.Any())
                {
                    MessageHelper.ShowValidationErrors(validationErrors);
                    return;
                }

                var loadingForm = MessageHelper.ShowLoadingMessage("جاري حفظ الوثيقة...");
                
                try
                {
                    var documentDto = CreateDocumentDto();
                    
                    DocumentDto savedDocument;
                    if (_isEditMode)
                    {
                        savedDocument = await _documentService.UpdateAsync(documentDto, _selectedFilePath);
                    }
                    else
                    {
                        savedDocument = await _documentService.CreateAsync(documentDto, _selectedFilePath);
                    }

                    if (savedDocument != null)
                    {
                        _currentDocument = savedDocument;
                        _hasUnsavedChanges = false;
                        UpdateFormTitle();
                        
                        MessageHelper.ShowSuccess(_isEditMode ? "تم تحديث الوثيقة بنجاح" : "تم إنشاء الوثيقة بنجاح");
                        
                        // Notify parent form
                        DocumentSaved?.Invoke(this, EventArgs.Empty);
                        
                        // Close form after successful save
                        Close();
                    }
                }
                finally
                {
                    MessageHelper.CloseLoadingMessage(loadingForm);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في حفظ الوثيقة");
            }
        }

        private System.Collections.Generic.List<string> ValidateForm()
        {
            var errors = new System.Collections.Generic.List<string>();

            if (string.IsNullOrWhiteSpace(titleTextBox.Text))
                errors.Add("عنوان الوثيقة مطلوب");

            if (string.IsNullOrWhiteSpace(senderRecipientTextBox.Text))
                errors.Add("المرسل/المستقبل مطلوب");

            if (typeComboBox.SelectedIndex < 0)
                errors.Add("نوع الوثيقة مطلوب");

            if (categoryComboBox.SelectedIndex < 0)
                errors.Add("فئة الوثيقة مطلوبة");

            if (statusComboBox.SelectedIndex < 0)
                errors.Add("حالة الوثيقة مطلوبة");

            if (datePicker.Value > DateTime.Now.Date)
                errors.Add("تاريخ الوثيقة لا يمكن أن يكون في المستقبل");

            // Validate file if selected
            if (!string.IsNullOrEmpty(_selectedFilePath))
            {
                if (!File.Exists(_selectedFilePath))
                    errors.Add("الملف المحدد غير موجود");
                else if (!_fileService.IsValidFileType(_selectedFilePath))
                    errors.Add("نوع الملف غير مدعوم");
            }

            return errors;
        }

        private DocumentDto CreateDocumentDto()
        {
            var typeItem = (dynamic)typeComboBox.SelectedItem;
            var categoryItem = (dynamic)categoryComboBox.SelectedItem;
            var statusItem = (dynamic)statusComboBox.SelectedItem;

            return new DocumentDto
            {
                Id = _currentDocument?.Id ?? 0,
                Title = titleTextBox.Text.Trim(),
                DocumentNumber = documentNumberTextBox.Text.Trim(),
                Date = datePicker.Value.Date,
                Type = (DocumentType)typeItem.Value,
                SenderRecipient = senderRecipientTextBox.Text.Trim(),
                CategoryId = (int)categoryItem.Value,
                Description = descriptionTextBox.Text.Trim(),
                Status = (DocumentStatus)statusItem.Value,
                OriginalFileName = !string.IsNullOrEmpty(_selectedFilePath) ? Path.GetFileName(_selectedFilePath) : _currentDocument?.OriginalFileName
            };
        }

        private void browseFileButton_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Title = "اختر ملف الوثيقة";
                openFileDialog.Filter = Business.Helpers.FileHelper.GetFileFilter();
                openFileDialog.Multiselect = false;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    _selectedFilePath = openFileDialog.FileName;
                    var fileInfo = new FileInfo(_selectedFilePath);
                    fileInfoLabel.Text = $"الملف المحدد: {fileInfo.Name} ({Business.Helpers.FileHelper.FormatFileSize(fileInfo.Length)})";
                    removeFileButton.Visible = true;
                    _hasUnsavedChanges = true;
                    UpdateFormTitle();
                }
            }
        }

        private void removeFileButton_Click(object sender, EventArgs e)
        {
            if (MessageHelper.ShowConfirmation("هل تريد إزالة الملف المرفق؟"))
            {
                _selectedFilePath = null;
                fileInfoLabel.Text = "لا يوجد ملف مرفق";
                removeFileButton.Visible = false;
                _hasUnsavedChanges = true;
                UpdateFormTitle();
            }
        }

        private void cancelButton_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void DocumentForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageHelper.ShowUnsavedChangesConfirmation();
                if (result == DialogResult.Yes)
                {
                    e.Cancel = true;
                    saveButton_Click(sender, e);
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
        }
    }
}
