using System;
using System.Data.Entity.Migrations;
using System.Linq;
using DocumentArchive.Data.Context;
using DocumentArchive.Models.Entities;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Data.Migrations
{
    internal sealed class Configuration : DbMigrationsConfiguration<DocumentArchiveContext>
    {
        public Configuration()
        {
            AutomaticMigrationsEnabled = true;
            AutomaticMigrationDataLossAllowed = false;
            ContextKey = "DocumentArchive.Data.Context.DocumentArchiveContext";
        }

        protected override void Seed(DocumentArchiveContext context)
        {
            // Seed default categories
            SeedCategories(context);
            
            // Seed sample documents
            SeedSampleDocuments(context);
        }

        private void SeedCategories(DocumentArchiveContext context)
        {
            var categories = new[]
            {
                new Category { Name = "العقود", Description = "العقود والاتفاقيات", IsActive = true },
                new Category { Name = "الفواتير", Description = "الفواتير والمطالبات المالية", IsActive = true },
                new Category { Name = "التقارير", Description = "التقارير الإدارية والفنية", IsActive = true },
                new Category { Name = "المراسلات", Description = "المراسلات الرسمية", IsActive = true },
                new Category { Name = "الوثائق القانونية", Description = "الوثائق والمستندات القانونية", IsActive = true },
                new Category { Name = "وثائق الموارد البشرية", Description = "وثائق الموظفين والموارد البشرية", IsActive = true },
                new Category { Name = "السجلات المالية", Description = "السجلات والوثائق المالية", IsActive = true }
            };

            foreach (var category in categories)
            {
                if (!context.Categories.Any(c => c.Name == category.Name))
                {
                    context.Categories.AddOrUpdate(c => c.Name, category);
                }
            }

            context.SaveChanges();
        }

        private void SeedSampleDocuments(DocumentArchiveContext context)
        {
            // Only seed if no documents exist
            if (context.Documents.Any()) return;

            var categories = context.Categories.ToList();
            if (!categories.Any()) return;

            var random = new Random();
            var sampleDocuments = new[]
            {
                new Document
                {
                    Title = "عقد توريد معدات مكتبية",
                    DocumentNumber = "IN-2024-0001",
                    Date = DateTime.Now.AddDays(-30),
                    Type = DocumentType.Incoming,
                    SenderRecipient = "شركة المعدات المكتبية المحدودة",
                    CategoryId = categories.First(c => c.Name == "العقود").Id,
                    Description = "عقد توريد معدات مكتبية للعام 2024",
                    Status = DocumentStatus.Processed
                },
                new Document
                {
                    Title = "فاتورة خدمات الصيانة",
                    DocumentNumber = "IN-2024-0002",
                    Date = DateTime.Now.AddDays(-25),
                    Type = DocumentType.Incoming,
                    SenderRecipient = "شركة الصيانة الشاملة",
                    CategoryId = categories.First(c => c.Name == "الفواتير").Id,
                    Description = "فاتورة صيانة أجهزة الكمبيوتر",
                    Status = DocumentStatus.Archived
                },
                new Document
                {
                    Title = "تقرير الأداء الشهري",
                    DocumentNumber = "OUT-2024-0001",
                    Date = DateTime.Now.AddDays(-20),
                    Type = DocumentType.Outgoing,
                    SenderRecipient = "الإدارة العليا",
                    CategoryId = categories.First(c => c.Name == "التقارير").Id,
                    Description = "تقرير أداء القسم لشهر ديسمبر",
                    Status = DocumentStatus.Processed
                },
                new Document
                {
                    Title = "خطاب شكر وتقدير",
                    DocumentNumber = "OUT-2024-0002",
                    Date = DateTime.Now.AddDays(-15),
                    Type = DocumentType.Outgoing,
                    SenderRecipient = "موظفو القسم",
                    CategoryId = categories.First(c => c.Name == "المراسلات").Id,
                    Description = "خطاب شكر للموظفين المتميزين",
                    Status = DocumentStatus.Draft
                },
                new Document
                {
                    Title = "وثيقة تأمين المبنى",
                    DocumentNumber = "IN-2024-0003",
                    Date = DateTime.Now.AddDays(-10),
                    Type = DocumentType.Incoming,
                    SenderRecipient = "شركة التأمين الوطنية",
                    CategoryId = categories.First(c => c.Name == "الوثائق القانونية").Id,
                    Description = "وثيقة تأمين شامل للمبنى",
                    Status = DocumentStatus.Processed
                }
            };

            foreach (var document in sampleDocuments)
            {
                if (!context.Documents.Any(d => d.DocumentNumber == document.DocumentNumber))
                {
                    context.Documents.AddOrUpdate(d => d.DocumentNumber, document);
                }
            }

            context.SaveChanges();
        }
    }
}
