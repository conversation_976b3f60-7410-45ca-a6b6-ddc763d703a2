@echo off
echo ========================================
echo Document Archive System - Quick Run
echo ========================================

echo.
echo Checking if application is built...

if not exist "DocumentArchive.UI\bin\Debug\DocumentArchive.exe" (
    echo Application not found. Building first...
    call build.bat
    if %errorlevel% neq 0 (
        echo ERROR: Build failed
        pause
        exit /b 1
    )
)

echo.
echo Starting Document Archive System...
echo.

cd DocumentArchive.UI\bin\Debug
start DocumentArchive.exe

echo Application started successfully!
echo.
echo If you encounter any issues:
echo 1. Check the Logs folder for error details
echo 2. Ensure .NET Framework 4.8 is installed
echo 3. Run setup-dev.bat for environment check
echo.
