# دليل البناء والتطوير
## Build and Development Guide

### متطلبات التطوير

#### البرامج المطلوبة
- **Visual Studio 2019** أو أحدث (Community/Professional/Enterprise)
- **.NET Framework 4.8 Developer Pack**
- **Git** لإدارة الإصدارات
- **SQLite Browser** (اختياري) لفحص قاعدة البيانات

#### إعدادات Visual Studio المطلوبة
- **Workloads:**
  - .NET desktop development
  - Data storage and processing (للعمل مع SQLite)

### خطوات الإعداد الأولي

#### 1. استنساخ المستودع
```bash
git clone [repository-url]
cd DocumentArchive
```

#### 2. استعادة حزم NuGet
```bash
# من سطر الأوامر
nuget restore DocumentArchive.sln

# أو من Visual Studio
Tools > NuGet Package Manager > Restore NuGet Packages
```

#### 3. بناء الحل
```bash
# من سطر الأوامر
msbuild DocumentArchive.sln /p:Configuration=Debug

# أو من Visual Studio
Build > Build Solution (Ctrl+Shift+B)
```

### هيكل المشروع التفصيلي

```
DocumentArchive/
├── DocumentArchive.sln              # ملف الحل الرئيسي
├── packages.config                  # حزم NuGet للحل
├── nuget.config                     # إعدادات NuGet
├── README.md                        # دليل المستخدم
├── BUILD.md                         # دليل البناء (هذا الملف)
├── .gitignore                       # ملفات Git المتجاهلة
│
├── DocumentArchive.Models/          # طبقة النماذج
│   ├── Entities/                    # كيانات قاعدة البيانات
│   │   ├── Document.cs
│   │   └── Category.cs
│   ├── Enums/                       # التعدادات
│   │   ├── DocumentType.cs
│   │   └── DocumentStatus.cs
│   ├── DTOs/                        # كائنات نقل البيانات
│   │   ├── DocumentDto.cs
│   │   ├── CategoryDto.cs
│   │   └── SearchCriteriaDto.cs
│   └── Properties/
│       └── AssemblyInfo.cs
│
├── DocumentArchive.Data/            # طبقة الوصول للبيانات
│   ├── Context/                     # سياق قاعدة البيانات
│   │   └── DocumentArchiveContext.cs
│   ├── Interfaces/                  # واجهات المستودعات
│   │   ├── IDocumentRepository.cs
│   │   ├── ICategoryRepository.cs
│   │   └── IUnitOfWork.cs
│   ├── Repositories/                # تطبيقات المستودعات
│   │   ├── DocumentRepository.cs
│   │   ├── CategoryRepository.cs
│   │   └── UnitOfWork.cs
│   ├── Migrations/                  # هجرات قاعدة البيانات
│   │   └── Configuration.cs
│   ├── App.config                   # إعدادات قاعدة البيانات
│   ├── packages.config              # حزم NuGet
│   └── Properties/
│       └── AssemblyInfo.cs
│
├── DocumentArchive.Business/        # طبقة منطق الأعمال
│   ├── Interfaces/                  # واجهات الخدمات
│   │   ├── IDocumentService.cs
│   │   ├── ICategoryService.cs
│   │   └── IFileService.cs
│   ├── Services/                    # تطبيقات الخدمات
│   │   ├── DocumentService.cs
│   │   ├── CategoryService.cs
│   │   └── FileService.cs
│   ├── Mappers/                     # محولات البيانات
│   │   ├── DocumentMapper.cs
│   │   └── CategoryMapper.cs
│   ├── Validators/                  # مدققات البيانات
│   │   ├── DocumentValidator.cs
│   │   └── CategoryValidator.cs
│   ├── Helpers/                     # مساعدات
│   │   ├── FileHelper.cs
│   │   └── NumberingHelper.cs
│   ├── packages.config
│   └── Properties/
│       └── AssemblyInfo.cs
│
├── DocumentArchive.UI/              # طبقة واجهة المستخدم
│   ├── Forms/                       # النماذج الرئيسية
│   │   ├── MainForm.cs/.Designer.cs/.resx
│   │   ├── DocumentForm.cs/.Designer.cs/.resx
│   │   └── CategoryForm.cs/.Designer.cs/.resx
│   ├── Controls/                    # التحكمات المخصصة
│   │   ├── SearchPanel.cs/.Designer.cs/.resx
│   │   └── DocumentPreviewPanel.cs/.Designer.cs/.resx
│   ├── Helpers/                     # مساعدات واجهة المستخدم
│   │   ├── UIHelper.cs
│   │   └── MessageHelper.cs
│   ├── Extensions/                  # امتدادات التحكمات
│   │   └── ControlExtensions.cs
│   ├── Resources/                   # الموارد (أيقونات، صور)
│   ├── Properties/                  # خصائص التطبيق
│   │   ├── AssemblyInfo.cs
│   │   ├── Resources.resx/.Designer.cs
│   │   └── Settings.settings/.Designer.cs
│   ├── Program.cs                   # نقطة دخول التطبيق
│   ├── App.config                   # إعدادات التطبيق
│   ├── NLog.config                  # إعدادات السجلات
│   └── packages.config
│
└── DocumentArchive.Tests/           # اختبارات الوحدة
    ├── Business/                    # اختبارات طبقة الأعمال
    │   ├── DocumentServiceTests.cs
    │   ├── CategoryServiceTests.cs
    │   └── FileServiceTests.cs
    ├── Helpers/                     # اختبارات المساعدات
    │   ├── FileHelperTests.cs
    │   └── NumberingHelperTests.cs
    ├── Mappers/                     # اختبارات المحولات
    │   ├── DocumentMapperTests.cs
    │   └── CategoryMapperTests.cs
    ├── packages.config
    └── Properties/
        └── AssemblyInfo.cs
```

### إعدادات البناء

#### Debug Configuration
- **Target Framework:** .NET Framework 4.8
- **Platform Target:** Any CPU
- **Debug Symbols:** Full
- **Optimize Code:** False
- **Define Constants:** DEBUG;TRACE

#### Release Configuration
- **Target Framework:** .NET Framework 4.8
- **Platform Target:** Any CPU
- **Debug Symbols:** PDB-only
- **Optimize Code:** True
- **Define Constants:** TRACE

### قواعد البناء

#### 1. ترتيب البناء
```
1. DocumentArchive.Models
2. DocumentArchive.Data
3. DocumentArchive.Business
4. DocumentArchive.UI
5. DocumentArchive.Tests
```

#### 2. التبعيات
- **UI** ← Business ← Data ← Models
- **Tests** ← Business, Data, Models

### اختبار المشروع

#### تشغيل جميع الاختبارات
```bash
# من سطر الأوامر
dotnet test DocumentArchive.Tests

# من Visual Studio
Test > Run All Tests (Ctrl+R, A)
```

#### تشغيل اختبارات محددة
```bash
# اختبارات فئة معينة
dotnet test --filter "ClassName=NumberingHelperTests"

# اختبارات طريقة معينة
dotnet test --filter "MethodName=GetDocumentPrefix_IncomingType_ReturnsIN"
```

### نشر التطبيق

#### إنشاء حزمة النشر
1. **من Visual Studio:**
   - Right-click على DocumentArchive.UI
   - Publish...
   - اختر Folder
   - حدد مجلد الوجهة
   - انقر Publish

2. **من سطر الأوامر:**
```bash
msbuild DocumentArchive.UI/DocumentArchive.UI.csproj /p:Configuration=Release /p:PublishProfile=FolderProfile
```

#### محتويات حزمة النشر
```
Release/
├── DocumentArchive.exe              # التطبيق الرئيسي
├── DocumentArchive.exe.config       # إعدادات التطبيق
├── NLog.config                      # إعدادات السجلات
├── *.dll                           # مكتبات التطبيق
├── Data/                           # مجلد قاعدة البيانات
├── Documents/                      # مجلد الوثائق
├── Logs/                          # مجلد السجلات
└── Resources/                     # الموارد
```

### استكشاف أخطاء البناء

#### مشاكل شائعة

**خطأ: "Could not load file or assembly"**
```bash
# الحل: استعادة حزم NuGet
nuget restore
```

**خطأ: "The type or namespace name could not be found"**
```bash
# الحل: التأكد من ترتيب البناء الصحيح
# بناء المشاريع بالترتيب: Models → Data → Business → UI
```

**خطأ: "SQLite provider not found"**
```bash
# الحل: التأكد من تثبيت حزم SQLite
Install-Package System.Data.SQLite.EF6
```

#### فحص السجلات
```bash
# فحص سجلات البناء
msbuild DocumentArchive.sln /flp:logfile=build.log;verbosity=diagnostic
```

### إرشادات التطوير

#### معايير الكود
- استخدم **PascalCase** للفئات والطرق
- استخدم **camelCase** للمتغيرات المحلية
- أضف تعليقات XML للطرق العامة
- اتبع مبادئ SOLID

#### Git Workflow
```bash
# إنشاء فرع جديد للميزة
git checkout -b feature/new-feature

# إضافة التغييرات
git add .
git commit -m "Add new feature"

# دفع الفرع
git push origin feature/new-feature

# إنشاء Pull Request
```

#### اختبار الكود
- اكتب اختبارات وحدة لكل طريقة عامة
- حقق تغطية 70% على الأقل
- اختبر السيناريوهات الإيجابية والسلبية

### الأدوات المساعدة

#### محررات مفيدة
- **Visual Studio Code** للتعديل السريع
- **SQL Server Management Studio** لفحص قاعدة البيانات
- **Postman** لاختبار APIs (إذا أضيفت لاحقاً)

#### إضافات Visual Studio مفيدة
- **ReSharper** لتحسين الكود
- **CodeMaid** لتنظيف الكود
- **Git Extensions** لإدارة Git

### الدعم والمساعدة

للحصول على مساعدة في البناء أو التطوير:
1. راجع هذا الدليل أولاً
2. تحقق من السجلات للأخطاء
3. ابحث في Issues الموجودة
4. أنشئ Issue جديد مع تفاصيل المشكلة
