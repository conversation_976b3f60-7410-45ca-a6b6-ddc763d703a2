using System.Collections.Generic;
using System.Threading.Tasks;
using DocumentArchive.Models.DTOs;

namespace DocumentArchive.Business.Interfaces
{
    /// <summary>
    /// Service interface for data export operations
    /// </summary>
    public interface IExportService
    {
        /// <summary>
        /// Export documents to Excel format
        /// </summary>
        Task<byte[]> ExportDocumentsToExcelAsync(List<DocumentDto> documents, string title = null);

        /// <summary>
        /// Export categories to Excel format
        /// </summary>
        Task<byte[]> ExportCategoriesToExcelAsync(List<CategoryDto> categories, string title = null);

        /// <summary>
        /// Export statistics to Excel format
        /// </summary>
        Task<byte[]> ExportStatisticsToExcelAsync(Dictionary<string, int> statistics, string title = null);

        /// <summary>
        /// Export documents to CSV format
        /// </summary>
        Task<byte[]> ExportDocumentsToCsvAsync(List<DocumentDto> documents);

        /// <summary>
        /// Export categories to CSV format
        /// </summary>
        Task<byte[]> ExportCategoriesToCsvAsync(List<CategoryDto> categories);

        /// <summary>
        /// Generate PDF report from documents
        /// </summary>
        Task<byte[]> GeneratePdfReportAsync(List<DocumentDto> documents, string reportTitle, string reportType = "summary");

        /// <summary>
        /// Generate PDF statistics report
        /// </summary>
        Task<byte[]> GeneratePdfStatisticsReportAsync(Dictionary<string, int> statistics, List<CategoryDto> categories, string title = null);

        /// <summary>
        /// Export search results to Excel
        /// </summary>
        Task<byte[]> ExportSearchResultsToExcelAsync(SearchCriteriaDto criteria, List<DocumentDto> results);

        /// <summary>
        /// Get supported export formats
        /// </summary>
        List<string> GetSupportedFormats();

        /// <summary>
        /// Get file extension for format
        /// </summary>
        string GetFileExtension(string format);

        /// <summary>
        /// Get MIME type for format
        /// </summary>
        string GetMimeType(string format);
    }
}
