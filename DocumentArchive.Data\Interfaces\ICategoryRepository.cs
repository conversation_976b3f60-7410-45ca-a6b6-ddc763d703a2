using System.Collections.Generic;
using System.Threading.Tasks;
using DocumentArchive.Models.Entities;

namespace DocumentArchive.Data.Interfaces
{
    /// <summary>
    /// Repository interface for Category operations
    /// </summary>
    public interface ICategoryRepository
    {
        /// <summary>
        /// Get category by ID
        /// </summary>
        Task<Category> GetByIdAsync(int id);

        /// <summary>
        /// Get all categories
        /// </summary>
        Task<List<Category>> GetAllAsync();

        /// <summary>
        /// Get active categories only
        /// </summary>
        Task<List<Category>> GetActiveAsync();

        /// <summary>
        /// Add new category
        /// </summary>
        Task<Category> AddAsync(Category category);

        /// <summary>
        /// Update existing category
        /// </summary>
        Task<Category> UpdateAsync(Category category);

        /// <summary>
        /// Delete category (soft delete by setting IsActive = false)
        /// </summary>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Check if category name exists
        /// </summary>
        Task<bool> NameExistsAsync(string name, int? excludeId = null);

        /// <summary>
        /// Get category with document count
        /// </summary>
        Task<Category> GetWithDocumentCountAsync(int id);

        /// <summary>
        /// Search categories by name
        /// </summary>
        Task<List<Category>> SearchAsync(string searchText);
    }
}
