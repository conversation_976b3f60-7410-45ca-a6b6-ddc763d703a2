using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using DocumentArchive.Business.Helpers;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Business.Mappers;
using DocumentArchive.Business.Validators;
using DocumentArchive.Data.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using NLog;

namespace DocumentArchive.Business.Services
{
    /// <summary>
    /// Service for Document operations
    /// </summary>
    public class DocumentService : IDocumentService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IFileService _fileService;
        private readonly DocumentValidator _validator;

        public DocumentService(IUnitOfWork unitOfWork, IFileService fileService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _validator = new DocumentValidator(_unitOfWork);
        }

        public async Task<DocumentDto> GetByIdAsync(int id)
        {
            try
            {
                var document = await _unitOfWork.Documents.GetByIdAsync(id);
                return DocumentMapper.ToDto(document);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error getting document by ID: {id}");
                throw;
            }
        }

        public async Task<PagedResult<DocumentDto>> GetAllAsync(SearchCriteriaDto criteria)
        {
            try
            {
                var pagedDocuments = await _unitOfWork.Documents.GetAllAsync(criteria);
                return DocumentMapper.ToPagedDto(pagedDocuments);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error getting documents with criteria");
                throw;
            }
        }

        public async Task<DocumentDto> CreateAsync(DocumentDto documentDto, string filePath = null)
        {
            try
            {
                // Generate document number if not provided
                if (string.IsNullOrEmpty(documentDto.DocumentNumber))
                {
                    documentDto.DocumentNumber = await GenerateDocumentNumberAsync(documentDto.Type);
                }

                // Validate input
                var validationErrors = await _validator.ValidateAsync(documentDto, false);
                if (validationErrors.Count > 0)
                {
                    throw new ArgumentException($"Validation failed: {string.Join(", ", validationErrors)}");
                }

                var document = DocumentMapper.ToEntity(documentDto);
                document.CreatedDate = DateTime.Now;
                document.ModifiedDate = DateTime.Now;

                // Handle file upload if provided
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    var fileValidationErrors = _validator.ValidateFile(filePath, documentDto.OriginalFileName);
                    if (fileValidationErrors.Count > 0)
                    {
                        throw new ArgumentException($"File validation failed: {string.Join(", ", fileValidationErrors)}");
                    }

                    // Save the document first to get the ID
                    var createdDocument = await _unitOfWork.Documents.AddAsync(document);
                    await _unitOfWork.SaveChangesAsync();

                    // Now save the file with the document ID
                    var savedFilePath = await _fileService.SaveFileAsync(
                        filePath,
                        documentDto.OriginalFileName ?? Path.GetFileName(filePath),
                        createdDocument.Id,
                        documentDto.Date.Year,
                        NumberingHelper.GetDocumentPrefix(documentDto.Type)
                    );

                    // Update document with file information
                    createdDocument.FilePath = savedFilePath;
                    createdDocument.OriginalFileName = documentDto.OriginalFileName ?? Path.GetFileName(filePath);
                    createdDocument.FileSize = _fileService.GetFileSize(savedFilePath);
                    createdDocument.MimeType = _fileService.GetMimeType(createdDocument.OriginalFileName);

                    await _unitOfWork.Documents.UpdateAsync(createdDocument);
                    await _unitOfWork.SaveChangesAsync();

                    Logger.Info($"Document created successfully with file: {createdDocument.DocumentNumber} (ID: {createdDocument.Id})");
                    return DocumentMapper.ToDto(createdDocument);
                }
                else
                {
                    var createdDocument = await _unitOfWork.Documents.AddAsync(document);
                    await _unitOfWork.SaveChangesAsync();

                    Logger.Info($"Document created successfully: {createdDocument.DocumentNumber} (ID: {createdDocument.Id})");
                    return DocumentMapper.ToDto(createdDocument);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error creating document: {documentDto?.Title}");
                throw;
            }
        }

        public async Task<DocumentDto> UpdateAsync(DocumentDto documentDto, string newFilePath = null)
        {
            try
            {
                // Validate input
                var validationErrors = await _validator.ValidateAsync(documentDto, true);
                if (validationErrors.Count > 0)
                {
                    throw new ArgumentException($"Validation failed: {string.Join(", ", validationErrors)}");
                }

                var existingDocument = await _unitOfWork.Documents.GetByIdAsync(documentDto.Id);
                if (existingDocument == null)
                {
                    throw new ArgumentException("Document not found");
                }

                var oldFilePath = existingDocument.FilePath;

                // Update document properties
                DocumentMapper.UpdateEntityFromDto(existingDocument, documentDto);
                existingDocument.ModifiedDate = DateTime.Now;

                // Handle file replacement if provided
                if (!string.IsNullOrEmpty(newFilePath) && File.Exists(newFilePath))
                {
                    var fileValidationErrors = _validator.ValidateFile(newFilePath, documentDto.OriginalFileName);
                    if (fileValidationErrors.Count > 0)
                    {
                        throw new ArgumentException($"File validation failed: {string.Join(", ", fileValidationErrors)}");
                    }

                    // Save new file
                    var savedFilePath = await _fileService.SaveFileAsync(
                        newFilePath,
                        documentDto.OriginalFileName ?? Path.GetFileName(newFilePath),
                        existingDocument.Id,
                        documentDto.Date.Year,
                        NumberingHelper.GetDocumentPrefix(documentDto.Type)
                    );

                    // Update file information
                    existingDocument.FilePath = savedFilePath;
                    existingDocument.OriginalFileName = documentDto.OriginalFileName ?? Path.GetFileName(newFilePath);
                    existingDocument.FileSize = _fileService.GetFileSize(savedFilePath);
                    existingDocument.MimeType = _fileService.GetMimeType(existingDocument.OriginalFileName);

                    // Delete old file if it exists and is different from new file
                    if (!string.IsNullOrEmpty(oldFilePath) && oldFilePath != savedFilePath)
                    {
                        await _fileService.DeleteFileAsync(oldFilePath);
                    }
                }

                var updatedDocument = await _unitOfWork.Documents.UpdateAsync(existingDocument);
                await _unitOfWork.SaveChangesAsync();

                Logger.Info($"Document updated successfully: {updatedDocument.DocumentNumber} (ID: {updatedDocument.Id})");
                return DocumentMapper.ToDto(updatedDocument);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error updating document: {documentDto?.Title} (ID: {documentDto?.Id})");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var document = await _unitOfWork.Documents.GetByIdAsync(id);
                if (document == null)
                {
                    return false;
                }

                var result = await _unitOfWork.Documents.DeleteAsync(id);
                if (result)
                {
                    await _unitOfWork.SaveChangesAsync();
                    Logger.Info($"Document deleted successfully: {document.DocumentNumber} (ID: {id})");
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error deleting document: ID {id}");
                throw;
            }
        }

        public async Task<List<DocumentDto>> SearchAsync(string searchText)
        {
            try
            {
                var documents = await _unitOfWork.Documents.SearchAsync(searchText);
                return DocumentMapper.ToDtoList(documents);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error searching documents: {searchText}");
                throw;
            }
        }

        public async Task<List<DocumentDto>> GetByCategoryAsync(int categoryId)
        {
            try
            {
                var documents = await _unitOfWork.Documents.GetByCategoryAsync(categoryId);
                return DocumentMapper.ToDtoList(documents);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error getting documents by category: {categoryId}");
                throw;
            }
        }

        public async Task<List<DocumentDto>> GetByStatusAsync(DocumentStatus status)
        {
            try
            {
                var documents = await _unitOfWork.Documents.GetByStatusAsync(status);
                return DocumentMapper.ToDtoList(documents);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error getting documents by status: {status}");
                throw;
            }
        }

        public async Task<List<DocumentDto>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var documents = await _unitOfWork.Documents.GetByDateRangeAsync(fromDate, toDate);
                return DocumentMapper.ToDtoList(documents);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error getting documents by date range: {fromDate} - {toDate}");
                throw;
            }
        }

        public async Task<string> GenerateDocumentNumberAsync(DocumentType type)
        {
            try
            {
                var currentYear = NumberingHelper.GetCurrentYear();
                return await _unitOfWork.Documents.GetNextDocumentNumberAsync(type, currentYear);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error generating document number for type: {type}");
                throw;
            }
        }

        public async Task<List<string>> ValidateDocumentAsync(DocumentDto documentDto)
        {
            try
            {
                return await _validator.ValidateAsync(documentDto, documentDto.Id > 0);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error validating document: {documentDto?.Title}");
                throw;
            }
        }

        public async Task<bool> ChangeStatusAsync(int id, DocumentStatus newStatus)
        {
            try
            {
                var document = await GetByIdAsync(id);
                if (document == null)
                {
                    return false;
                }

                var validationErrors = _validator.ValidateStatusChange(document, newStatus);
                if (validationErrors.Count > 0)
                {
                    throw new ArgumentException($"Status change validation failed: {string.Join(", ", validationErrors)}");
                }

                document.Status = newStatus;
                await UpdateAsync(document);

                Logger.Info($"Document status changed successfully: {document.DocumentNumber} to {newStatus}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error changing document status: ID {id} to {newStatus}");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetStatisticsAsync()
        {
            try
            {
                var statistics = new Dictionary<string, int>();

                statistics["TotalDocuments"] = await _unitOfWork.Documents.GetCountByStatusAsync(DocumentStatus.Processed) +
                                             await _unitOfWork.Documents.GetCountByStatusAsync(DocumentStatus.Archived) +
                                             await _unitOfWork.Documents.GetCountByStatusAsync(DocumentStatus.Draft);

                statistics["IncomingDocuments"] = await _unitOfWork.Documents.GetCountByTypeAsync(DocumentType.Incoming);
                statistics["OutgoingDocuments"] = await _unitOfWork.Documents.GetCountByTypeAsync(DocumentType.Outgoing);
                statistics["DraftDocuments"] = await _unitOfWork.Documents.GetCountByStatusAsync(DocumentStatus.Draft);
                statistics["ProcessedDocuments"] = await _unitOfWork.Documents.GetCountByStatusAsync(DocumentStatus.Processed);
                statistics["ArchivedDocuments"] = await _unitOfWork.Documents.GetCountByStatusAsync(DocumentStatus.Archived);

                return statistics;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error getting document statistics");
                throw;
            }
        }

        public async Task<byte[]> ExportToExcelAsync(SearchCriteriaDto criteria)
        {
            try
            {
                // This would be implemented using EPPlus library
                // For now, return empty array as placeholder
                await Task.CompletedTask;
                return new byte[0];
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error exporting documents to Excel");
                throw;
            }
        }
    }
}
