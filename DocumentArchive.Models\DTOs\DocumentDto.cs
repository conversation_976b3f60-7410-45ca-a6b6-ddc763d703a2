using System;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for Document
    /// </summary>
    public class DocumentDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string DocumentNumber { get; set; }
        public DateTime Date { get; set; }
        public DocumentType Type { get; set; }
        public string TypeDisplayName { get; set; }
        public string SenderRecipient { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public string Description { get; set; }
        public DocumentStatus Status { get; set; }
        public string StatusDisplayName { get; set; }
        public string FilePath { get; set; }
        public string OriginalFileName { get; set; }
        public long? FileSize { get; set; }
        public string FileSizeFormatted { get; set; }
        public string MimeType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public bool HasFile => !string.IsNullOrEmpty(FilePath);
        public string FileExtension => HasFile ? System.IO.Path.GetExtension(OriginalFileName) : string.Empty;
    }
}
