using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DocumentArchive.Data.Interfaces;
using DocumentArchive.Models.DTOs;

namespace DocumentArchive.Business.Validators
{
    /// <summary>
    /// Validator for Category operations
    /// </summary>
    public class CategoryValidator
    {
        private readonly IUnitOfWork _unitOfWork;

        public CategoryValidator(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        /// <summary>
        /// Validate category data
        /// </summary>
        public async Task<List<string>> ValidateAsync(CategoryDto categoryDto, bool isUpdate = false)
        {
            var errors = new List<string>();

            // Required field validations
            if (string.IsNullOrWhiteSpace(categoryDto.Name))
            {
                errors.Add("اسم الفئة مطلوب");
            }
            else if (categoryDto.Name.Length > 100)
            {
                errors.Add("اسم الفئة لا يمكن أن يتجاوز 100 حرف");
            }
            else
            {
                // Check for duplicate name
                var excludeId = isUpdate ? (int?)categoryDto.Id : null;
                var nameExists = await _unitOfWork.Categories.NameExistsAsync(categoryDto.Name, excludeId);
                if (nameExists)
                {
                    errors.Add("اسم الفئة موجود مسبقاً");
                }
            }

            // Optional field validations
            if (!string.IsNullOrWhiteSpace(categoryDto.Description) && categoryDto.Description.Length > 500)
            {
                errors.Add("وصف الفئة لا يمكن أن يتجاوز 500 حرف");
            }

            return errors;
        }

        /// <summary>
        /// Validate category deletion
        /// </summary>
        public async Task<List<string>> ValidateDeleteAsync(int categoryId)
        {
            var errors = new List<string>();

            var category = await _unitOfWork.Categories.GetByIdAsync(categoryId);
            if (category == null)
            {
                errors.Add("الفئة غير موجودة");
                return errors;
            }

            // Check if category has documents
            var documents = await _unitOfWork.Documents.GetByCategoryAsync(categoryId);
            if (documents.Count > 0)
            {
                errors.Add($"لا يمكن حذف الفئة لأنها تحتوي على {documents.Count} وثيقة. سيتم إلغاء تفعيل الفئة بدلاً من حذفها.");
            }

            return errors;
        }

        /// <summary>
        /// Validate category name
        /// </summary>
        public async Task<bool> IsNameUniqueAsync(string name, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(name)) return false;

            return !await _unitOfWork.Categories.NameExistsAsync(name, excludeId);
        }
    }
}
