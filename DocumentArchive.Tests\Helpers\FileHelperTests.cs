using System.IO;
using DocumentArchive.Business.Helpers;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DocumentArchive.Tests.Helpers
{
    [TestClass]
    public class FileHelperTests
    {
        [TestMethod]
        public void GetMimeType_PdfFile_ReturnsCorrectMimeType()
        {
            // Arrange
            var fileName = "document.pdf";

            // Act
            var result = FileHelper.GetMimeType(fileName);

            // Assert
            Assert.AreEqual("application/pdf", result);
        }

        [TestMethod]
        public void GetMimeType_WordFile_ReturnsCorrectMimeType()
        {
            // Arrange
            var fileName = "document.docx";

            // Act
            var result = FileHelper.GetMimeType(fileName);

            // Assert
            Assert.AreEqual("application/vnd.openxmlformats-officedocument.wordprocessingml.document", result);
        }

        [TestMethod]
        public void GetMimeType_UnknownExtension_ReturnsDefaultMimeType()
        {
            // Arrange
            var fileName = "document.unknown";

            // Act
            var result = FileHelper.GetMimeType(fileName);

            // Assert
            Assert.AreEqual("application/octet-stream", result);
        }

        [TestMethod]
        public void IsValidFileType_SupportedExtension_ReturnsTrue()
        {
            // Arrange
            var fileName = "document.pdf";

            // Act
            var result = FileHelper.IsValidFileType(fileName);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsValidFileType_UnsupportedExtension_ReturnsFalse()
        {
            // Arrange
            var fileName = "document.exe";

            // Act
            var result = FileHelper.IsValidFileType(fileName);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void FormatFileSize_ZeroBytes_ReturnsZeroB()
        {
            // Arrange
            long bytes = 0;

            // Act
            var result = FileHelper.FormatFileSize(bytes);

            // Assert
            Assert.AreEqual("0 B", result);
        }

        [TestMethod]
        public void FormatFileSize_1024Bytes_Returns1KB()
        {
            // Arrange
            long bytes = 1024;

            // Act
            var result = FileHelper.FormatFileSize(bytes);

            // Assert
            Assert.AreEqual("1 KB", result);
        }

        [TestMethod]
        public void FormatFileSize_1048576Bytes_Returns1MB()
        {
            // Arrange
            long bytes = 1048576; // 1024 * 1024

            // Act
            var result = FileHelper.FormatFileSize(bytes);

            // Assert
            Assert.AreEqual("1 MB", result);
        }

        [TestMethod]
        public void GenerateSafeFileName_ValidName_ReturnsUnchanged()
        {
            // Arrange
            var fileName = "document.pdf";

            // Act
            var result = FileHelper.GenerateSafeFileName(fileName);

            // Assert
            Assert.AreEqual("document.pdf", result);
        }

        [TestMethod]
        public void GenerateSafeFileName_InvalidCharacters_ReturnsCleanedName()
        {
            // Arrange
            var fileName = "doc<>ument.pdf";

            // Act
            var result = FileHelper.GenerateSafeFileName(fileName);

            // Assert
            Assert.AreEqual("document.pdf", result);
        }

        [TestMethod]
        public void IsImageFile_ImageExtension_ReturnsTrue()
        {
            // Arrange
            var fileName = "image.jpg";

            // Act
            var result = FileHelper.IsImageFile(fileName);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsImageFile_NonImageExtension_ReturnsFalse()
        {
            // Arrange
            var fileName = "document.pdf";

            // Act
            var result = FileHelper.IsImageFile(fileName);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsDocumentFile_DocumentExtension_ReturnsTrue()
        {
            // Arrange
            var fileName = "document.pdf";

            // Act
            var result = FileHelper.IsDocumentFile(fileName);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsDocumentFile_NonDocumentExtension_ReturnsFalse()
        {
            // Arrange
            var fileName = "image.jpg";

            // Act
            var result = FileHelper.IsDocumentFile(fileName);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void GetSupportedExtensions_ReturnsExpectedExtensions()
        {
            // Act
            var result = FileHelper.GetSupportedExtensions();

            // Assert
            Assert.IsTrue(result.Contains(".pdf"));
            Assert.IsTrue(result.Contains(".doc"));
            Assert.IsTrue(result.Contains(".docx"));
            Assert.IsTrue(result.Contains(".jpg"));
            Assert.IsTrue(result.Contains(".png"));
        }
    }
}
