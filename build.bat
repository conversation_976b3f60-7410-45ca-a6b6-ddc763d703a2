@echo off
echo ========================================
echo Building Document Archive System
echo ========================================

echo.
echo [1/5] Restoring NuGet packages...
nuget restore DocumentArchive.sln
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore NuGet packages
    pause
    exit /b 1
)

echo.
echo [2/5] Building Models project...
msbuild DocumentArchive.Models\DocumentArchive.Models.csproj /p:Configuration=Debug /p:Platform="Any CPU"
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Models project
    pause
    exit /b 1
)

echo.
echo [3/5] Building Data project...
msbuild DocumentArchive.Data\DocumentArchive.Data.csproj /p:Configuration=Debug /p:Platform="Any CPU"
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Data project
    pause
    exit /b 1
)

echo.
echo [4/5] Building Business project...
msbuild DocumentArchive.Business\DocumentArchive.Business.csproj /p:Configuration=Debug /p:Platform="Any CPU"
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Business project
    pause
    exit /b 1
)

echo.
echo [5/5] Building UI project...
msbuild DocumentArchive.UI\DocumentArchive.UI.csproj /p:Configuration=Debug /p:Platform="Any CPU"
if %errorlevel% neq 0 (
    echo ERROR: Failed to build UI project
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo You can now run the application from:
echo DocumentArchive.UI\bin\Debug\DocumentArchive.exe
echo.
pause
