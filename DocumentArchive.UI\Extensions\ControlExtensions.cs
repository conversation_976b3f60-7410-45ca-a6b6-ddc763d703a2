using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace DocumentArchive.UI.Extensions
{
    /// <summary>
    /// Extension methods for Windows Forms controls
    /// </summary>
    public static class ControlExtensions
    {
        /// <summary>
        /// Safely invoke action on UI thread
        /// </summary>
        public static void SafeInvoke(this Control control, Action action)
        {
            if (control == null || action == null) return;

            if (control.InvokeRequired)
            {
                control.Invoke(action);
            }
            else
            {
                action();
            }
        }

        /// <summary>
        /// Safely invoke function on UI thread
        /// </summary>
        public static T SafeInvoke<T>(this Control control, Func<T> func)
        {
            if (control == null || func == null) return default(T);

            if (control.InvokeRequired)
            {
                return (T)control.Invoke(func);
            }
            else
            {
                return func();
            }
        }

        /// <summary>
        /// Execute async operation with loading indicator
        /// </summary>
        public static async Task ExecuteWithLoadingAsync(this Control control, Func<Task> operation, string loadingText = null)
        {
            if (control == null || operation == null) return;

            var originalCursor = control.Cursor;
            var originalEnabled = control.Enabled;

            try
            {
                control.Cursor = Cursors.WaitCursor;
                control.Enabled = false;

                await operation();
            }
            finally
            {
                control.Cursor = originalCursor;
                control.Enabled = originalEnabled;
            }
        }

        /// <summary>
        /// Execute async operation with loading indicator and return result
        /// </summary>
        public static async Task<T> ExecuteWithLoadingAsync<T>(this Control control, Func<Task<T>> operation, string loadingText = null)
        {
            if (control == null || operation == null) return default(T);

            var originalCursor = control.Cursor;
            var originalEnabled = control.Enabled;

            try
            {
                control.Cursor = Cursors.WaitCursor;
                control.Enabled = false;

                return await operation();
            }
            finally
            {
                control.Cursor = originalCursor;
                control.Enabled = originalEnabled;
            }
        }

        /// <summary>
        /// Clear all text boxes in a container
        /// </summary>
        public static void ClearTextBoxes(this Control container)
        {
            if (container == null) return;

            foreach (Control control in container.Controls)
            {
                if (control is TextBox textBox)
                {
                    textBox.Clear();
                }
                else if (control.HasChildren)
                {
                    control.ClearTextBoxes();
                }
            }
        }

        /// <summary>
        /// Clear all combo boxes in a container
        /// </summary>
        public static void ClearComboBoxes(this Control container)
        {
            if (container == null) return;

            foreach (Control control in container.Controls)
            {
                if (control is ComboBox comboBox)
                {
                    comboBox.SelectedIndex = -1;
                }
                else if (control.HasChildren)
                {
                    control.ClearComboBoxes();
                }
            }
        }

        /// <summary>
        /// Reset all form controls to default state
        /// </summary>
        public static void ResetControls(this Control container)
        {
            if (container == null) return;

            container.ClearTextBoxes();
            container.ClearComboBoxes();

            foreach (Control control in container.Controls)
            {
                if (control is CheckBox checkBox)
                {
                    checkBox.Checked = false;
                }
                else if (control is RadioButton radioButton)
                {
                    radioButton.Checked = false;
                }
                else if (control is DateTimePicker dateTimePicker)
                {
                    dateTimePicker.Value = DateTime.Now;
                }
                else if (control.HasChildren)
                {
                    control.ResetControls();
                }
            }
        }

        /// <summary>
        /// Enable or disable all controls in a container
        /// </summary>
        public static void SetControlsEnabled(this Control container, bool enabled)
        {
            if (container == null) return;

            foreach (Control control in container.Controls)
            {
                control.Enabled = enabled;
                
                if (control.HasChildren)
                {
                    control.SetControlsEnabled(enabled);
                }
            }
        }

        /// <summary>
        /// Find control by name recursively
        /// </summary>
        public static T FindControlByName<T>(this Control container, string name) where T : Control
        {
            if (container == null || string.IsNullOrEmpty(name)) return null;

            foreach (Control control in container.Controls)
            {
                if (control.Name == name && control is T)
                {
                    return (T)control;
                }

                if (control.HasChildren)
                {
                    var found = control.FindControlByName<T>(name);
                    if (found != null) return found;
                }
            }

            return null;
        }

        /// <summary>
        /// Highlight control with error color
        /// </summary>
        public static void HighlightError(this Control control)
        {
            if (control == null) return;

            var originalBackColor = control.BackColor;
            control.BackColor = Color.FromArgb(255, 235, 235); // Light red

            // Reset after 3 seconds
            var timer = new Timer { Interval = 3000 };
            timer.Tick += (s, e) =>
            {
                control.BackColor = originalBackColor;
                timer.Stop();
                timer.Dispose();
            };
            timer.Start();
        }

        /// <summary>
        /// Highlight control with success color
        /// </summary>
        public static void HighlightSuccess(this Control control)
        {
            if (control == null) return;

            var originalBackColor = control.BackColor;
            control.BackColor = Color.FromArgb(235, 255, 235); // Light green

            // Reset after 2 seconds
            var timer = new Timer { Interval = 2000 };
            timer.Tick += (s, e) =>
            {
                control.BackColor = originalBackColor;
                timer.Stop();
                timer.Dispose();
            };
            timer.Start();
        }

        /// <summary>
        /// Animate control fade in
        /// </summary>
        public static void FadeIn(this Control control, int duration = 500)
        {
            if (control == null) return;

            control.Visible = true;
            var timer = new Timer { Interval = 50 };
            var steps = duration / timer.Interval;
            var currentStep = 0;

            timer.Tick += (s, e) =>
            {
                currentStep++;
                var opacity = (double)currentStep / steps;
                
                if (control is Form form)
                {
                    form.Opacity = opacity;
                }

                if (currentStep >= steps)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };

            timer.Start();
        }

        /// <summary>
        /// Animate control fade out
        /// </summary>
        public static void FadeOut(this Control control, int duration = 500, Action onComplete = null)
        {
            if (control == null) return;

            var timer = new Timer { Interval = 50 };
            var steps = duration / timer.Interval;
            var currentStep = 0;

            timer.Tick += (s, e) =>
            {
                currentStep++;
                var opacity = 1.0 - (double)currentStep / steps;
                
                if (control is Form form)
                {
                    form.Opacity = opacity;
                }

                if (currentStep >= steps)
                {
                    control.Visible = false;
                    timer.Stop();
                    timer.Dispose();
                    onComplete?.Invoke();
                }
            };

            timer.Start();
        }

        /// <summary>
        /// Set placeholder text for TextBox
        /// </summary>
        public static void SetPlaceholder(this TextBox textBox, string placeholder)
        {
            if (textBox == null || string.IsNullOrEmpty(placeholder)) return;

            var isPlaceholderActive = false;
            var originalForeColor = textBox.ForeColor;

            void ShowPlaceholder()
            {
                if (string.IsNullOrEmpty(textBox.Text))
                {
                    textBox.Text = placeholder;
                    textBox.ForeColor = Color.Gray;
                    isPlaceholderActive = true;
                }
            }

            void HidePlaceholder()
            {
                if (isPlaceholderActive)
                {
                    textBox.Text = "";
                    textBox.ForeColor = originalForeColor;
                    isPlaceholderActive = false;
                }
            }

            textBox.Enter += (s, e) => HidePlaceholder();
            textBox.Leave += (s, e) => ShowPlaceholder();

            // Show placeholder initially if text is empty
            ShowPlaceholder();
        }

        /// <summary>
        /// Auto-resize DataGridView columns
        /// </summary>
        public static void AutoResizeColumns(this DataGridView dgv)
        {
            if (dgv == null) return;

            dgv.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);
            
            // Ensure last column fills remaining space
            if (dgv.Columns.Count > 0)
            {
                dgv.Columns[dgv.Columns.Count - 1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
            }
        }

        /// <summary>
        /// Export DataGridView to CSV
        /// </summary>
        public static void ExportToCsv(this DataGridView dgv, string filePath)
        {
            if (dgv == null || string.IsNullOrEmpty(filePath)) return;

            using (var writer = new System.IO.StreamWriter(filePath, false, System.Text.Encoding.UTF8))
            {
                // Write headers
                var headers = new string[dgv.Columns.Count];
                for (int i = 0; i < dgv.Columns.Count; i++)
                {
                    headers[i] = dgv.Columns[i].HeaderText;
                }
                writer.WriteLine(string.Join(",", headers));

                // Write data
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    if (row.IsNewRow) continue;

                    var values = new string[dgv.Columns.Count];
                    for (int i = 0; i < dgv.Columns.Count; i++)
                    {
                        var value = row.Cells[i].Value?.ToString() ?? "";
                        values[i] = $"\"{value.Replace("\"", "\"\"")}\""; // Escape quotes
                    }
                    writer.WriteLine(string.Join(",", values));
                }
            }
        }
    }
}
